import { useState, useCallback } from 'react';
import type { MenuItem, OrderItem } from '../types';
import { TAX_RATE } from '../constants';

export interface UseOrderReturn {
  orderItems: OrderItem[];
  addItemToOrder: (item: MenuItem, quantityFromNumpad?: number) => void;
  removeItemFromOrder: (itemId: string) => void;
  updateItemQuantity: (itemId:string, quantity: number) => void;
  clearOrder: () => void;
  calculateSubtotal: () => number;
  calculateTax: (subtotal: number) => number;
  calculateTotal: () => number;
}

export const useOrder = (): UseOrderReturn => {
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);

  const addItemToOrder = useCallback((item: MenuItem, quantityFromNumpad?: number) => {
    setOrderItems((prevItems) => {
      const existingItem = prevItems.find((i) => i.id === item.id);
      if (existingItem) {
        if (quantityFromNumpad !== undefined && quantityFromNumpad > 0) {
          // Numpad quantity used, set the item's quantity to this new value
          return prevItems.map((i) =>
            i.id === item.id ? { ...i, quantity: quantityFromNumpad } : i
          );
        } else {
          // No numpad quantity, default behavior: increment quantity
          return prevItems.map((i) =>
            i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
          );
        }
      } else {
        // Item not in order, add it
        // Use numpad quantity if provided and valid, otherwise default to 1
        const initialQuantity = (quantityFromNumpad !== undefined && quantityFromNumpad > 0) ? quantityFromNumpad : 1;
        if (initialQuantity <= 0) return prevItems; // Do not add if quantity is zero or less
        return [...prevItems, { ...item, quantity: initialQuantity }];
      }
    });
  }, []);

  const removeItemFromOrder = useCallback((itemId: string) => {
    setOrderItems((prevItems) => prevItems.filter((i) => i.id !== itemId));
  }, []);

  const updateItemQuantity = useCallback((itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItemFromOrder(itemId);
      return;
    }
    setOrderItems((prevItems) =>
      prevItems.map((i) => (i.id === itemId ? { ...i, quantity } : i))
    );
  }, [removeItemFromOrder]);

  const clearOrder = useCallback(() => {
    setOrderItems([]);
  }, []);

  const calculateSubtotal = useCallback(() => {
    return orderItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
  }, [orderItems]);

  const calculateTax = useCallback((subtotal: number) => {
    return subtotal * TAX_RATE;
  }, []);

  const calculateTotal = useCallback(() => {
    const subtotal = calculateSubtotal();
    const tax = calculateTax(subtotal);
    return subtotal + tax;
  }, [calculateSubtotal, calculateTax]);

  return {
    orderItems,
    addItemToOrder,
    removeItemFromOrder,
    updateItemQuantity,
    clearOrder,
    calculateSubtotal,
    calculateTax,
    calculateTotal,
  };
};