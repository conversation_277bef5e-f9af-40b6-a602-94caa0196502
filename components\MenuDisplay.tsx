import React from 'react';
import type { MenuItem } from '../types';
import { MenuItemCard } from './MenuItemCard';

interface MenuDisplayProps {
  menuItems: MenuItem[];
  categories: string[];
  selectedCategory: string;
  onSelectCategory: (category: string) => void;
  onAddItem: (item: MenuItem) => void; // This function in App.tsx now handles numpad quantity
}

export const MenuDisplay: React.FC<MenuDisplayProps> = ({ 
  menuItems, 
  categories, 
  selectedCategory, 
  onSelectCategory, 
  onAddItem 
}) => {
  return (
    <div className="flex-grow bg-dark-card p-6 rounded-lg shadow-xl border border-dark-border flex flex-col overflow-y-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-brand-primary mb-4">Menu Categories</h2>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => onSelectCategory('All')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200
              ${selectedCategory === 'All' 
                ? 'bg-brand-primary text-dark-bg shadow-glow-primary' 
                : 'bg-slate-700 text-slate-300 hover:bg-slate-600'}`}
          >
            All Items
          </button>
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => onSelectCategory(category)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200
                ${selectedCategory === category 
                  ? 'bg-brand-primary text-dark-bg shadow-glow-primary' 
                  : 'bg-slate-700 text-slate-300 hover:bg-slate-600'}`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {menuItems.length === 0 && selectedCategory !== "All" && (
         <div className="flex-grow flex items-center justify-center">
            <p className="text-medium-text text-xl">No items in "{selectedCategory}".</p>
         </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-slate-700 scrollbar-track-slate-800">
        {menuItems.map((item) => (
          <MenuItemCard key={item.id} item={item} onAddItem={onAddItem} />
        ))}
      </div>
    </div>
  );
};