import type { Sale, SaleDetailsForStorage } from '../../types';

const API_URL = 'http://localhost:5000/api';

// Convertir une vente du format client au format serveur
const formatSaleForServer = (sale: Sale) => {
  return {
    timestamp: sale.timestamp,
    items: sale.items,
    subtotal: sale.subtotal,
    taxAmount: sale.taxAmount,
    totalAmount: sale.totalAmount
  };
};

// Enregistrer une vente dans la base de données
export const saveSaleToDatabase = async (sale: Sale): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${API_URL}/ventes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formatSaleForServer(sale)),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Erreur lors de l\'enregistrement de la vente');
    }

    return { success: true, message: 'Vente enregistrée avec succès' };
  } catch (error) {
    console.error('Erreur lors de l\'enregistrement de la vente:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Erreur inconnue'
    };
  }
};

// Récupérer toutes les ventes depuis la base de données
export const getSalesFromDatabase = async (): Promise<Sale[]> => {
  try {
    const response = await fetch(`${API_URL}/ventes`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Erreur lors de la récupération des ventes');
    }

    const ventesFromDb = await response.json();
    
    // Convertir le format de la base de données au format client
    return ventesFromDb.map((vente: any) => ({
      id: vente.id.toString(),
      timestamp: vente.timestamp,
      items: vente.items || [], // Si les items ne sont pas inclus dans la réponse
      subtotal: vente.subtotal,
      taxAmount: vente.tax_amount,
      totalAmount: vente.total_amount
    }));
  } catch (error) {
    console.error('Erreur lors de la récupération des ventes:', error);
    return [];
  }
};

// Récupérer une vente par son ID
export const getSaleById = async (id: string): Promise<Sale | null> => {
  try {
    const response = await fetch(`${API_URL}/ventes/${id}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Erreur lors de la récupération de la vente');
    }

    const vente = await response.json();
    
    // Convertir le format de la base de données au format client
    return {
      id: vente.id.toString(),
      timestamp: vente.timestamp,
      items: vente.items.map((item: any) => ({
        id: item.item_id,
        name: item.item_name,
        price: item.price,
        quantity: item.quantity,
        // Ces champs peuvent être manquants dans la base de données
        category: '',
        imageUrl: '',
        description: ''
      })),
      subtotal: vente.subtotal,
      taxAmount: vente.tax_amount,
      totalAmount: vente.total_amount
    };
  } catch (error) {
    console.error('Erreur lors de la récupération de la vente:', error);
    return null;
  }
};