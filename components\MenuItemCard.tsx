import React from 'react';
import type { MenuItem } from '../types';
import { FuturisticButton } from './FuturisticButton';

interface MenuItemCardProps {
  item: MenuItem;
  onAddItem: (item: MenuItem) => void; // This function in App.tsx now handles numpad quantity
}

const PlusIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>
);


export const MenuItemCard: React.FC<MenuItemCardProps> = ({ item, onAddItem }) => {
  return (
    <div className="bg-dark-card rounded-lg shadow-lg overflow-hidden transition-all duration-300 hover:shadow-glow-secondary border border-transparent hover:border-brand-secondary/50 flex flex-col animation-subtle-slide-in">
      <img src={item.imageUrl} alt={item.name} className="w-full h-40 object-cover" />
      <div className="p-4 flex flex-col flex-grow">
        <h3 className="text-xl font-semibold text-light-text mb-1 truncate">{item.name}</h3>
        <p className="text-xs text-brand-primary uppercase tracking-wider mb-2">{item.category}</p>
        <p className="text-sm text-medium-text mb-3 flex-grow min-h-[40px]">{item.description}</p>
        <div className="flex justify-between items-center mt-auto">
          <p className="text-2xl font-bold text-brand-primary">${item.price.toFixed(2)}</p>
          <FuturisticButton
            onClick={() => onAddItem(item)} // onAddItem from App.tsx will use Numpad value
            variant="primary"
            size="sm"
            icon={<PlusIcon className="w-4 h-4" />}
          >
            Add
          </FuturisticButton>
        </div>
      </div>
    </div>
  );
};