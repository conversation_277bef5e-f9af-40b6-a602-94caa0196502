
import React from 'react';
import type { OrderItem } from '../types';

interface OrderItemRowProps {
  item: OrderItem;
  onRemoveItem: (itemId: string) => void;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
}

const TrashIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
 <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12.56 0c1.153 0 2.24.032 3.22.096m4.908 0a48.667 48.667 0 0 1-7.628 0m7.628 0a48.108 48.108 0 0 1 3.478-.397m0_0H19.5M5.636 5.79m10.182 0a48.108 48.108 0 0 0-3.478-.397m-4.908 0c1.153 0 2.24.032 3.22.096m0_0H19.5" />
  </svg>
);

const MinusIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M5 12h14" />
  </svg>
);

const PlusIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>
);


export const OrderItemRow: React.FC<OrderItemRowProps> = ({ item, onRemoveItem, onUpdateQuantity }) => {
  return (
    <div className="flex items-center justify-between py-3 px-2 border-b border-dark-border/50 hover:bg-slate-700/30 transition-colors duration-150">
      <div className="flex items-center w-2/5">
        <img src={item.imageUrl} alt={item.name} className="w-10 h-10 rounded-md object-cover mr-3 border border-dark-border" />
        <div>
          <p className="text-sm font-medium text-light-text truncate w-32" title={item.name}>{item.name}</p>
          <p className="text-xs text-medium-text">${item.price.toFixed(2)} each</p>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <button
          onClick={() => onUpdateQuantity(item.id, item.quantity - 1)}
          className="p-1 rounded-full text-medium-text hover:bg-slate-600 hover:text-light-text transition-colors"
          aria-label="Decrease quantity"
        >
          <MinusIcon className="w-4 h-4" />
        </button>
        <span className="text-sm w-6 text-center font-medium">{item.quantity}</span>
        <button
          onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
          className="p-1 rounded-full text-medium-text hover:bg-slate-600 hover:text-light-text transition-colors"
          aria-label="Increase quantity"
        >
          <PlusIcon className="w-4 h-4" />
        </button>
      </div>
      <p className="text-sm font-semibold text-brand-primary w-1/6 text-right">${(item.price * item.quantity).toFixed(2)}</p>
      <button
        onClick={() => onRemoveItem(item.id)}
        className="text-red-500 hover:text-red-400 transition-colors p-1 rounded-full hover:bg-red-500/20"
        aria-label="Remove item"
      >
        <TrashIcon className="w-5 h-5" />
      </button>
    </div>
  );
};
    