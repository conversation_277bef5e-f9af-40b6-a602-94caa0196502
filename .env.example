# Configuration de l'application QuantumServe POS
# Copier ce fichier vers .env et modifier les valeurs selon vos besoins

# Clé secrète pour Flask (générer une clé unique pour la production)
SECRET_KEY=quantum-pos-secret-key-2024

# URL de la base de données
DATABASE_URL=sqlite:///pos_quantum.db

# Environnement Flask (development, production)
FLASK_ENV=development

# Port d'écoute (optionnel, par défaut 5000)
PORT=5000

# Configuration de debug (true/false)
DEBUG=true
