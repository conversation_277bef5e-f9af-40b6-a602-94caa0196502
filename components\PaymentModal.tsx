
import React from 'react';
import type { OrderItem } from '../types';
import { FuturisticButton } from './FuturisticButton';
import { TAX_RATE } from '../constants';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderItems: OrderItem[];
  totalAmount: number;
}

const CheckCircleIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

export const PaymentModal: React.FC<PaymentModalProps> = ({ isOpen, onClose, orderItems, totalAmount }) => {
  if (!isOpen) return null;

  const subtotal = orderItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const taxAmount = subtotal * TAX_RATE;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-dark-card rounded-xl shadow-2xl p-8 w-full max-w-md border border-brand-secondary/50 transform transition-all animate-subtle-slide-in">
        <div className="flex flex-col items-center text-center mb-6">
          <CheckCircleIcon className="w-16 h-16 text-green-400 mb-4 shadow-glow-primary rounded-full" style={{ ['--glow-color' as string]: 'rgba(74, 222, 128, 0.5)'}} />
          <h2 className="text-3xl font-bold text-brand-primary mb-2">Payment Successful!</h2>
          <p className="text-medium-text">Thank you for your order.</p>
        </div>

        <div className="max-h-60 overflow-y-auto mb-6 pr-2 scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-slate-700 border-t border-b border-dark-border py-4">
          <h3 className="text-lg font-semibold text-light-text mb-2">Order Summary:</h3>
          {orderItems.map(item => (
            <div key={item.id} className="flex justify-between items-center py-1.5 text-sm">
              <span className="text-medium-text">{item.name} x {item.quantity}</span>
              <span className="text-light-text">${(item.price * item.quantity).toFixed(2)}</span>
            </div>
          ))}
        </div>
        
        <div className="space-y-1 mb-6 text-sm">
            <div className="flex justify-between text-medium-text">
              <span>Subtotal:</span>
              <span className="text-light-text">${subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-medium-text">
              <span>Tax ({ (TAX_RATE * 100).toFixed(0) }%):</span>
              <span className="text-light-text">${taxAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-xl font-bold text-brand-primary pt-2 border-t border-dark-border mt-2">
              <span>Total Paid:</span>
              <span>${totalAmount.toFixed(2)}</span>
            </div>
          </div>

        <FuturisticButton onClick={onClose} variant="secondary" size="lg" fullWidth>
          Close & New Order
        </FuturisticButton>
      </div>
    </div>
  );
};
    