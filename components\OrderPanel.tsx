
import React from 'react';
import type { OrderItem } from '../types';
import { OrderItemRow } from './OrderItemRow';
import { FuturisticButton } from './FuturisticButton';
import { TAX_RATE } from '../constants';

interface OrderPanelProps {
  orderItems: OrderItem[];
  total: number;
  onRemoveItem: (itemId: string) => void;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onClearOrder: () => void;
  onProcessPayment: () => void;
}

const CreditCardIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
  </svg>
);

const XCircleIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);


export const OrderPanel: React.FC<OrderPanelProps> = ({
  orderItems,
  total,
  onRemoveItem,
  onUpdateQuantity,
  onClearOrder,
  onProcessPayment,
}) => {
  const subtotal = orderItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const taxAmount = subtotal * TAX_RATE;

  return (
    <div className="w-full md:w-2/5 lg:w-1/3 bg-dark-card p-6 rounded-lg shadow-xl border border-dark-border flex flex-col">
      <h2 className="text-2xl font-bold text-brand-primary mb-1">Current Order</h2>
      <p className="text-xs text-medium-text mb-4 border-b border-dark-border pb-4">Manage items and proceed to payment.</p>
      
      {orderItems.length === 0 ? (
        <div className="flex-grow flex flex-col items-center justify-center text-center">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-24 h-24 text-slate-700 mb-4">
            <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z" />
          </svg>
          <p className="text-medium-text text-lg">Your order is empty.</p>
          <p className="text-sm text-slate-500">Add items from the menu to get started.</p>
        </div>
      ) : (
        <div className="flex-grow overflow-y-auto pr-2 -mr-2 scrollbar-thin scrollbar-thumb-slate-700 scrollbar-track-slate-800">
          {orderItems.map((item) => (
            <OrderItemRow
              key={item.id}
              item={item}
              onRemoveItem={onRemoveItem}
              onUpdateQuantity={onUpdateQuantity}
            />
          ))}
        </div>
      )}

      {orderItems.length > 0 && (
        <div className="mt-auto pt-6 border-t border-dark-border">
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm text-medium-text">
              <span>Subtotal:</span>
              <span>${subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm text-medium-text">
              <span>Tax ({ (TAX_RATE * 100).toFixed(0) }%):</span>
              <span>${taxAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-xl font-bold text-brand-primary">
              <span>Total:</span>
              <span>${total.toFixed(2)}</span>
            </div>
          </div>
          <div className="space-y-3">
            <FuturisticButton
              onClick={onProcessPayment}
              variant="primary"
              size="lg"
              fullWidth
              icon={<CreditCardIcon className="w-5 h-5" />}
              disabled={orderItems.length === 0}
            >
              Process Payment
            </FuturisticButton>
            <FuturisticButton
              onClick={onClearOrder}
              variant="danger"
              size="md"
              fullWidth
              icon={<XCircleIcon className="w-5 h-5" />}
              disabled={orderItems.length === 0}
            >
              Clear Order
            </FuturisticButton>
          </div>
        </div>
      )}
    </div>
  );
};
    