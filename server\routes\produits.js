import express from 'express';
import { 
  createProduit, 
  getProduits, 
  getProduitById, 
  updateProduit, 
  deleteProduit 
} from '../models/produit.js';

const router = express.Router();

// Créer un nouveau produit
router.post('/', (req, res) => {
  try {
    const id = createProduit(req.body);
    res.status(201).json({ id, ...req.body });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Récupérer tous les produits
router.get('/', (req, res) => {
  try {
    const produits = getProduits();
    res.json(produits);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Récupérer un produit par son ID
router.get('/:id', (req, res) => {
  try {
    const produit = getProduitById(req.params.id);
    if (produit) {
      res.json(produit);
    } else {
      res.status(404).json({ error: 'Produit non trouvé' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Mettre à jour un produit
router.put('/:id', (req, res) => {
  try {
    const updated = updateProduit(req.params.id, req.body);
    if (updated) {
      res.json({ id: req.params.id, ...req.body });
    } else {
      res.status(404).json({ error: 'Produit non trouvé' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Supprimer un produit
router.delete('/:id', (req, res) => {
  try {
    const deleted = deleteProduit(req.params.id);
    if (deleted) {
      res.status(204).send();
    } else {
      res.status(404).json({ error: 'Produit non trouvé' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

export default router;
