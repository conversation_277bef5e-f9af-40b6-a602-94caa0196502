/* Variables CSS globales pour les thèmes - Version corrigée 2025-05-27 */
:root {
    /* Th<PERSON> (par défaut) */
    --bg-primary: #020617;
    --bg-secondary: #1e293b;
    --bg-card: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #94a3b8;
    --border-color: #334155;
    --brand-primary: #22d3ee;
    --brand-secondary: #a855f7;
    --brand-accent: #ec4899;
    --shadow-color: rgba(34, 211, 238, 0.4);
    --hover-shadow: 0 0 20px 3px rgba(34, 211, 238, 0.3);
}

/* Thème Classique */
.classic-theme {
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-card: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --brand-primary: #3b82f6;
    --brand-secondary: #6366f1;
    --brand-accent: #8b5cf6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Styles de base utilisant les variables CSS */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

#app-header {
    background-color: var(--bg-card);
    border-color: var(--border-color);
}

.bg-dark-surface {
    background-color: var(--bg-primary) !important;
}

.bg-dark-card {
    background-color: var(--bg-card) !important;
    border-color: var(--border-color) !important;
}

.border-dark-border {
    border-color: var(--border-color) !important;
}

.text-light-text {
    color: var(--text-primary) !important;
}

.text-medium-text {
    color: var(--text-secondary) !important;
}

.text-brand-primary {
    color: var(--brand-primary) !important;
}

.text-brand-secondary {
    color: var(--brand-secondary) !important;
}

.bg-brand-primary {
    background-color: var(--brand-primary) !important;
}

.hover\:bg-cyan-500:hover {
    background-color: var(--brand-primary) !important;
}

/* Boutons de navigation */
.nav-btn {
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background-color: var(--brand-primary);
    color: var(--bg-primary);
}

.nav-btn.active {
    background-color: var(--brand-primary);
    color: var(--bg-primary);
    box-shadow: 0 0 15px 2px var(--shadow-color);
}

/* Bouton de changement de thème */
.theme-toggle-btn {
    background-color: var(--brand-secondary);
    color: var(--bg-primary);
    border: 1px solid var(--brand-secondary);
    transition: all 0.3s ease;
}

.theme-toggle-btn:hover {
    background-color: var(--brand-accent);
    transform: scale(1.05);
}

/* Cartes de produits */
.product-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

/* Boutons de catégorie */
.category-btn {
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.category-btn:hover {
    background-color: var(--brand-primary);
    color: var(--bg-primary);
}

.category-btn.active {
    background-color: var(--brand-primary);
    color: var(--bg-primary);
}

/* Articles de commande */
.order-item {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

/* Pavé numérique */
.numpad-container {
    background-color: var(--bg-card);
    border-color: var(--border-color);
}

.numpad-btn {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.numpad-btn:hover {
    background-color: var(--brand-primary);
    color: var(--bg-primary);
    transform: scale(1.05);
}

.numpad-btn:active {
    transform: scale(0.95);
}

/* Affichage du pavé numérique */
.numpad-display {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    color: var(--brand-primary);
}

/* Modal de paiement */
.payment-modal .modal-content {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    box-shadow: 0 25px 50px -12px var(--shadow-color);
}

/* Panneau de commande */
.order-panel {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

/* Boutons d'action */
.action-buttons button {
    transition: all 0.3s ease;
}

/* Boutons de quantité */
.quantity-btn, .remove-btn {
    transition: all 0.2s ease;
}

/* Pages de contenu */
.page-content {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* Cartes de vente */
.sale-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.sale-card:hover {
    border-color: var(--brand-primary);
    box-shadow: var(--hover-shadow);
}

/* En-têtes de date */
.date-header {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

/* Animations spéciales pour le thème futuriste */
:root .glow-effect {
    animation: pulse-glow 2s infinite ease-in-out;
}

.classic-theme .glow-effect {
    animation: none;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 10px 1px rgba(34, 211, 238, 0.3);
    }
    50% {
        box-shadow: 0 0 20px 3px rgba(34, 211, 238, 0.5);
    }
}

/* Amélioration de la visibilité pour le thème classique */
.classic-theme .text-brand-primary {
    font-weight: 600;
}

.classic-theme .nav-btn.active {
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.classic-theme .theme-toggle-btn {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Styles responsifs */
@media (max-width: 768px) {
    .numpad-container {
        display: none;
    }

    .order-panel {
        width: 100%;
        max-width: 320px;
    }
}
