
import React from 'react';

interface FuturisticButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

export const FuturisticButton: React.FC<FuturisticButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  fullWidth = false,
  className,
  ...props
}) => {
  const baseStyle = "font-semibold rounded-md transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-opacity-50 transform hover:scale-105 active:scale-95 flex items-center justify-center shadow-md";

  let variantStyle = '';
  switch (variant) {
    case 'primary':
      variantStyle = 'bg-brand-primary text-dark-bg hover:bg-cyan-300 focus:ring-brand-primary shadow-glow-primary hover:shadow-glow-primary';
      break;
    case 'secondary':
      variantStyle = 'bg-brand-secondary text-light-text hover:bg-purple-400 focus:ring-brand-secondary shadow-glow-secondary hover:shadow-glow-secondary';
      break;
    case 'danger':
      variantStyle = 'bg-red-600 text-light-text hover:bg-red-500 focus:ring-red-500 shadow-md hover:shadow-lg';
      break;
    case 'ghost':
      variantStyle = 'bg-transparent border-2 border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-dark-bg focus:ring-brand-primary';
      break;
  }

  let sizeStyle = '';
  switch (size) {
    case 'sm':
      sizeStyle = 'px-3 py-1.5 text-sm';
      break;
    case 'md':
      sizeStyle = 'px-4 py-2 text-base';
      break;
    case 'lg':
      sizeStyle = 'px-6 py-3 text-lg';
      break;
  }
  
  const widthStyle = fullWidth ? 'w-full' : '';

  return (
    <button
      className={`${baseStyle} ${variantStyle} ${sizeStyle} ${widthStyle} ${className || ''}`}
      {...props}
    >
      {icon && <span className="mr-2">{icon}</span>}
      {children}
    </button>
  );
};
    