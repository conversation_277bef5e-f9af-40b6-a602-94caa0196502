<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuantumServe POS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/themes.css') }}">
    <script>
        // Configuration Tailwind avec thèmes
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // Thème Futuriste
                        'brand-primary': '#22d3ee',
                        'brand-secondary': '#a855f7',
                        'brand-accent': '#ec4899',
                        'dark-bg': '#0f172a',
                        'dark-card': '#1e293b',
                        'dark-surface': '#020617',
                        'light-text': '#f1f5f9',
                        'medium-text': '#94a3b8',
                        'dark-border': '#334155',

                        // Thème Classique
                        'classic-primary': '#3b82f6',
                        'classic-secondary': '#6366f1',
                        'classic-accent': '#8b5cf6',
                        'classic-bg': '#f8fafc',
                        'classic-card': '#ffffff',
                        'classic-surface': '#f1f5f9',
                        'classic-text': '#1e293b',
                        'classic-text-light': '#64748b',
                        'classic-border': '#e2e8f0',
                    },
                    boxShadow: {
                        'glow-primary': '0 0 15px 2px rgba(34, 211, 238, 0.4)',
                        'glow-secondary': '0 0 15px 2px rgba(168, 85, 247, 0.4)',
                        'glow-accent': '0 0 15px 2px rgba(236, 72, 153, 0.4)',
                        'classic-soft': '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        'classic-medium': '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                    },
                    animation: {
                        'pulse-glow': 'pulse-glow 2s infinite ease-in-out',
                        'subtle-slide-in': 'subtle-slide-in 0.5s ease-out forwards',
                        'theme-transition': 'theme-transition 0.3s ease-in-out',
                    },
                    keyframes: {
                        'pulse-glow': {
                            '0%, 100%': { opacity: '0.7', boxShadow: '0 0 10px 1px var(--glow-color, rgba(34, 211, 238, 0.3))' },
                            '50%': { opacity: '1', boxShadow: '0 0 20px 3px var(--glow-color, rgba(34, 211, 238, 0.5))' },
                        },
                        'subtle-slide-in': {
                            '0%': { opacity: '0', transform: 'translateY(10px)' },
                            '100%': { opacity: '1', transform: 'translateY(0px)' },
                        },
                        'theme-transition': {
                            '0%': { opacity: '0.8' },
                            '100%': { opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Styles pour les barres de défilement */
        .scrollbar-thin::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        .scrollbar-thin::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
            border-radius: 10px;
        }
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: 10px;
        }
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: var(--scrollbar-thumb-hover);
        }

        /* Variables CSS pour les thèmes */
        :root {
            --scrollbar-track: #1e293b;
            --scrollbar-thumb: #334155;
            --scrollbar-thumb-hover: #475569;
        }

        .classic-theme {
            --scrollbar-track: #f1f5f9;
            --scrollbar-thumb: #cbd5e1;
            --scrollbar-thumb-hover: #94a3b8;
        }

        /* Styles pour Chart.js */
        .chart-container {
            position: relative;
            margin: auto;
            height: 60vh;
            width: 80vw;
            max-width: 800px;
            min-height: 300px;
        }

        @media (max-width: 768px) {
            .chart-container {
                height: 50vh;
                width: 90vw;
            }
        }

        /* Transition fluide entre les thèmes */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Amélioration de la visibilité */
        .classic-theme {
            --tw-text-opacity: 1;
        }

        .classic-theme .bg-dark-surface {
            background-color: #f8fafc !important;
        }

        .classic-theme .bg-dark-card {
            background-color: #ffffff !important;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
        }

        .classic-theme .text-light-text {
            color: #1e293b !important;
        }

        .classic-theme .border-dark-border {
            border-color: #e2e8f0 !important;
        }
    </style>
</head>
<body id="app-body" class="bg-dark-surface text-light-text antialiased font-sans">
    <div id="root" class="flex flex-col h-screen">
        <!-- Header avec bouton de changement de thème -->
        <header id="app-header" class="bg-dark-card border-b border-dark-border px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <h1 class="text-2xl font-bold text-brand-primary">QuantumServe POS</h1>
                <nav class="flex space-x-2">
                    <button id="nav-pos" class="nav-btn active px-4 py-2 rounded-lg font-medium transition-all duration-200">
                        POS
                    </button>
                    <button id="nav-sales" class="nav-btn px-4 py-2 rounded-lg font-medium transition-all duration-200">
                        Ventes
                    </button>
                    <button id="nav-reports" class="nav-btn px-4 py-2 rounded-lg font-medium transition-all duration-200">
                        Rapports
                    </button>
                </nav>
            </div>

            <!-- Bouton de changement de thème -->
            <div class="flex items-center space-x-4">
                <button id="theme-toggle" class="theme-toggle-btn px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2">
                    <span id="theme-icon">🌙</span>
                    <span id="theme-text">Classique</span>
                </button>
            </div>
        </header>

        <!-- Contenu principal -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Page POS -->
            <div id="pos-page" class="page-content flex flex-1 overflow-hidden">
                <!-- Pavé numérique -->
                <div id="numpad" class="numpad-container bg-dark-card border-r border-dark-border p-4 flex flex-col space-y-2">
                    <div class="numpad-display bg-dark-surface border border-dark-border rounded-lg p-4 text-center">
                        <span id="numpad-value" class="text-2xl font-mono text-brand-primary">0</span>
                    </div>
                    <div class="numpad-grid grid grid-cols-3 gap-2 flex-1">
                        <!-- Les boutons du pavé numérique seront générés par JavaScript -->
                    </div>
                </div>

                <!-- Zone principale -->
                <main class="flex flex-1 overflow-hidden p-4 space-x-4">
                    <!-- Menu des produits -->
                    <div class="menu-section flex-1 flex flex-col">
                        <!-- Filtres de catégories -->
                        <div id="category-filters" class="category-filters mb-4 flex flex-wrap gap-2">
                            <!-- Les boutons de catégories seront générés par JavaScript -->
                        </div>

                        <!-- Grille des produits -->
                        <div id="menu-grid" class="menu-grid flex-1 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 overflow-y-auto scrollbar-thin">
                            <!-- Les cartes de produits seront générées par JavaScript -->
                        </div>
                    </div>

                    <!-- Panneau de commande -->
                    <div class="order-panel w-80 bg-dark-card rounded-lg p-4 flex flex-col">
                        <h2 class="text-xl font-semibold text-brand-secondary mb-4">Commande</h2>

                        <!-- Liste des articles -->
                        <div id="order-items" class="order-items flex-1 overflow-y-auto scrollbar-thin space-y-2 mb-4">
                            <!-- Les articles de la commande seront générés par JavaScript -->
                        </div>

                        <!-- Résumé -->
                        <div class="order-summary border-t border-dark-border pt-4 space-y-2">
                            <div class="flex justify-between">
                                <span>Sous-total:</span>
                                <span id="subtotal">0,00 €</span>
                            </div>
                            <div class="flex justify-between">
                                <span>TVA (8%):</span>
                                <span id="tax">0,00 €</span>
                            </div>
                            <div class="flex justify-between font-bold text-lg">
                                <span>Total:</span>
                                <span id="total" class="text-brand-primary">0,00 €</span>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="action-buttons mt-4 space-y-2">
                            <button id="clear-order" class="w-full py-2 bg-red-600 hover:bg-red-700 rounded-lg font-medium transition-colors">
                                Vider
                            </button>
                            <button id="process-payment" class="w-full py-3 bg-brand-primary hover:bg-cyan-500 text-dark-surface rounded-lg font-bold transition-all duration-200 transform hover:scale-105">
                                Payer
                            </button>
                        </div>
                    </div>
                </main>
            </div>

            <!-- Page Ventes -->
            <div id="sales-page" class="page-content hidden flex-1 p-6 overflow-y-auto scrollbar-thin">
                <div class="max-w-6xl mx-auto">
                    <h1 class="text-3xl font-bold text-brand-primary mb-6">Historique des Ventes</h1>
                    <div id="sales-list" class="space-y-4">
                        <!-- Les ventes seront chargées par JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Page Rapports -->
            <div id="reports-page" class="page-content hidden flex-1 p-6 overflow-y-auto scrollbar-thin">
                <div class="max-w-6xl mx-auto">
                    <h1 class="text-3xl font-bold text-brand-primary mb-6">Rapports de Ventes</h1>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-dark-card p-4 rounded-lg shadow-xl border border-dark-border">
                            <h2 class="text-xl font-semibold text-brand-secondary mb-3">Ventes dans le temps</h2>
                            <div class="chart-container">
                                <canvas id="sales-over-time-chart"></canvas>
                            </div>
                        </div>

                        <div class="bg-dark-card p-4 rounded-lg shadow-xl border border-dark-border">
                            <h2 class="text-xl font-semibold text-brand-secondary mb-3">Articles populaires</h2>
                            <div class="chart-container">
                                <canvas id="top-items-chart"></canvas>
                            </div>
                        </div>

                        <div class="bg-dark-card p-4 rounded-lg shadow-xl border border-dark-border lg:col-span-2">
                            <h2 class="text-xl font-semibold text-brand-secondary mb-3">Revenus par catégorie</h2>
                            <div class="chart-container max-w-md mx-auto">
                                <canvas id="revenue-by-category-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de paiement -->
    <div id="payment-modal" class="payment-modal fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="modal-content bg-dark-card rounded-lg p-6 max-w-md w-full mx-4">
            <h2 class="text-2xl font-bold text-brand-primary mb-4">Paiement</h2>
            <div id="payment-summary" class="mb-6">
                <!-- Le résumé du paiement sera généré par JavaScript -->
            </div>
            <div class="flex space-x-4">
                <button id="cancel-payment" class="flex-1 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors">
                    Annuler
                </button>
                <button id="confirm-payment" class="flex-1 py-2 bg-brand-primary hover:bg-cyan-500 text-dark-surface rounded-lg font-bold transition-colors">
                    Confirmer
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/themes.js') }}"></script>
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script src="{{ url_for('static', filename='js/pos.js') }}"></script>
    <script src="{{ url_for('static', filename='js/sales.js') }}"></script>
    <script src="{{ url_for('static', filename='js/reports.js') }}"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
