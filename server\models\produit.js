import { db } from '../db/database.js';

// Créer un nouveau produit
export const createProduit = (produit) => {
  const { nom, description, prix, quantite } = produit;
  const stmt = db.prepare(
    'INSERT INTO produits (nom, description, prix, quantite) VALUES (?, ?, ?, ?)'
  );
  const result = stmt.run(nom, description, prix, quantite);
  return result.lastInsertRowid;
};

// Récupérer tous les produits
export const getProduits = () => {
  const stmt = db.prepare('SELECT * FROM produits ORDER BY date_creation DESC');
  return stmt.all();
};

// Récupérer un produit par son ID
export const getProduitById = (id) => {
  const stmt = db.prepare('SELECT * FROM produits WHERE id = ?');
  return stmt.get(id);
};

// Mettre à jour un produit
export const updateProduit = (id, produit) => {
  const { nom, description, prix, quantite } = produit;
  const stmt = db.prepare(
    'UPDATE produits SET nom = ?, description = ?, prix = ?, quantite = ? WHERE id = ?'
  );
  return stmt.run(nom, description, prix, quantite, id).changes > 0;
};

// Supprimer un produit
export const deleteProduit = (id) => {
  const stmt = db.prepare('DELETE FROM produits WHERE id = ?');
  return stmt.run(id).changes > 0;
};
