<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>QuantumServe POS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'brand-primary': '#22d3ee', // cyan-400
              'brand-secondary': '#a855f7', // purple-500
              'brand-accent': '#ec4899', // pink-500
              'dark-bg': '#0f172a', // slate-900
              'dark-card': '#1e293b', // slate-800
              'dark-surface': '#020617', // slate-950
              'light-text': '#f1f5f9', // slate-100
              'medium-text': '#94a3b8', // slate-400
              'dark-border': '#334155', // slate-700
            },
            boxShadow: {
              'glow-primary': '0 0 15px 2px rgba(34, 211, 238, 0.4)',
              'glow-secondary': '0 0 15px 2px rgba(168, 85, 247, 0.4)',
              'glow-accent': '0 0 15px 2px rgba(236, 72, 153, 0.4)',
            },
            animation: {
              'pulse-glow': 'pulse-glow 2s infinite ease-in-out',
              'subtle-slide-in': 'subtle-slide-in 0.5s ease-out forwards',
            },
            keyframes: {
              'pulse-glow': {
                '0%, 100%': { opacity: '0.7', boxShadow: '0 0 10px 1px var(--glow-color, rgba(34, 211, 238, 0.3))' },
                '50%': { opacity: '1', boxShadow: '0 0 20px 3px var(--glow-color, rgba(34, 211, 238, 0.5))' },
              },
              'subtle-slide-in': {
                '0%': { opacity: '0', transform: 'translateY(10px)' },
                '100%': { opacity: '1', transform: 'translateY(0px)' },
              }
            }
          }
        },
        plugins: [
          // require('tailwind-scrollbar')({ nocompatible: true }), // Keep this commented if CDN setup is preferred
        ]
      }
    </script>
    <style>
      /* Custom scrollbar styles for WebKit browsers */
      .scrollbar-thin::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      .scrollbar-thin::-webkit-scrollbar-track {
        background: #1e293b; /* slate-800 */
        border-radius: 10px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background: #334155; /* slate-700 */
        border-radius: 10px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb:hover {
        background: #475569; /* slate-600 */
      }

      /* For Firefox - basic styling */
      .scrollbar-thin {
        scrollbar-width: thin;
        scrollbar-color: #334155 #1e293b; /* slate-700 slate-800 */
      }

      /* Chart.js responsive canvas */
      .chart-container {
        position: relative;
        margin: auto;
        height: 60vh; /* Adjust as needed */
        width: 80vw;   /* Adjust as needed */
        max-width: 800px; /* Max width for larger screens */
        min-height: 300px; /* Ensure a minimum height */
      }
      
      @media (max-width: 768px) {
        .chart-container {
          height: 50vh;
          width: 90vw;
        }
      }

    </style>
  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "chart.js/auto": "https://esm.sh/chart.js@^4.4.3/auto",
    "chart.js": "https://esm.sh/chart.js@^4.4.9",
    "chart.js/": "https://esm.sh/chart.js@^4.4.9/"
  }
}
</script>
</head>
  <body class="bg-dark-surface text-light-text antialiased">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
