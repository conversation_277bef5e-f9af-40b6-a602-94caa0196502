import type { Sale } from '../types';

const SALES_STORAGE_KEY = 'quantumServeSales';

export const saveSaleToLocalStorage = (sale: Sale): void => {
  try {
    const existingSales = getSalesFromLocalStorage();
    const updatedSales = [...existingSales, sale];
    localStorage.setItem(SALES_STORAGE_KEY, JSON.stringify(updatedSales));
  } catch (error) {
    console.error("Error saving sale to localStorage:", error);
    // Optionally, implement a more robust error handling or fallback
  }
};

export const getSalesFromLocalStorage = (): Sale[] => {
  try {
    const salesJson = localStorage.getItem(SALES_STORAGE_KEY);
    if (salesJson) {
      const sales = JSON.parse(salesJson) as Sale[];
      // Basic validation might be good here if structure could change
      return sales.map(sale => ({
        ...sale,
        // Ensure numbers are numbers, not strings after parse
        subtotal: Number(sale.subtotal),
        taxAmount: Number(sale.taxAmount),
        totalAmount: Number(sale.totalAmount),
        items: sale.items.map(item => ({
            ...item,
            price: Number(item.price),
            quantity: Number(item.quantity)
        }))
      }));
    }
  } catch (error) {
    console.error("Error retrieving sales from localStorage:", error);
    // localStorage might be disabled or full
  }
  return [];
};
