import React, { useState, useCallback } from 'react';
import { Header } from './components/Header';
import { MenuDisplay } from './components/MenuDisplay';
import { OrderPanel } from './components/OrderPanel';
import { PaymentModal } from './components/PaymentModal';
import { Numpad } from './components/Numpad';
import { SalesView } from './components/SalesView';
import { ReportsView } from './components/ReportsView';
import { useOrder } from './hooks/useOrder';
import type { MenuItem, OrderItem, Sale, SaleDetailsForStorage, Page } from './types';
import { MENU_ITEMS } from './constants';
import { saveSaleToLocalStorage } from './utils/salesStorage';
import { saveSaleToDatabase } from './src/services/venteService';

const App: React.FC = () => {
  const {
    orderItems,
    addItemToOrder,
    removeItemFromOrder,
    updateItemQuantity,
    clearOrder,
    calculateSubtotal,
    calculateTax,
    calculateTotal,
  } = useOrder();

  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [orderToPayForModal, setOrderToPayForModal] = useState<OrderItem[] | null>(null);
  const [paymentTotalForModal, setPaymentTotalForModal] = useState(0);
  const [saleDetailsForSave, setSaleDetailsForSave] = useState<SaleDetailsForStorage | null>(null);

  const uniqueCategories = Array.from(new Set(MENU_ITEMS.map(item => item.category)));
  const [selectedCategory, setSelectedCategory] = useState<string>(uniqueCategories[0] || 'All');
  const [currentPage, setCurrentPage] = useState<Page>('pos');
  const [numpadDisplayValue, setNumpadDisplayValue] = useState<string>('');

  const handlePayment = useCallback(() => {
    if (orderItems.length === 0) return;

    const sub = calculateSubtotal();
    const tax = calculateTax(sub);
    const total = calculateTotal();

    setOrderToPayForModal([...orderItems]);
    setPaymentTotalForModal(total);
    setSaleDetailsForSave({
        items: [...orderItems],
        subtotal: sub,
        taxAmount: tax,
        totalAmount: total,
    });
    setIsPaymentModalOpen(true);
  }, [orderItems, calculateTotal, calculateSubtotal, calculateTax]);

  const handleCloseModal = useCallback(() => {
    if (saleDetailsForSave) {
      const newSale: Sale = {
        id: `sale_${new Date().getTime()}_${Math.random().toString(36).substring(2, 8)}`,
        timestamp: new Date().toISOString(),
        items: saleDetailsForSave.items,
        subtotal: saleDetailsForSave.subtotal,
        taxAmount: saleDetailsForSave.taxAmount,
        totalAmount: saleDetailsForSave.totalAmount,
      };
      
      // Enregistrer la vente dans le stockage local
      saveSaleToLocalStorage(newSale);
      
      // Enregistrer la vente dans la base de données
      saveSaleToDatabase(newSale)
        .then(result => {
          if (result.success) {
            console.log('Vente enregistrée dans la base de données:', result.message);
          } else {
            console.error('Erreur lors de l\'enregistrement dans la base de données:', result.message);
          }
        })
        .catch(error => {
          console.error('Erreur lors de l\'enregistrement dans la base de données:', error);
        });
    }
    setIsPaymentModalOpen(false);
    clearOrder();
    setOrderToPayForModal(null);
    setPaymentTotalForModal(0);
    setSaleDetailsForSave(null);
  }, [clearOrder, saleDetailsForSave]);
  
  const filteredMenuItems = selectedCategory === 'All' 
    ? MENU_ITEMS 
    : MENU_ITEMS.filter(item => item.category === selectedCategory);

  const handleNumpadInput = (digit: string) => {
    setNumpadDisplayValue(prev => {
      if (prev === '0' && digit === '0') return '0'; // Avoid multiple leading zeros
      if (prev === '0' && digit !== '0') return digit; // Replace single 0
      const newValue = prev + digit;
      return newValue.length > 3 ? newValue.slice(0, 3) : newValue; // Max 3 digits for quantity
    });
  };

  const handleNumpadClear = () => {
    setNumpadDisplayValue('');
  };

  const handleNumpadBackspace = () => {
    setNumpadDisplayValue(prev => prev.slice(0, -1));
  };
  
  const handleNumpadEnter = () => {
    // This could be used to apply numpad value to a selected order item in the future
    // For now, it primarily serves to confirm the numpad input before clicking an item
    // Or, it could just clear the numpad if that's the desired UX.
    // console.log("Numpad Entered:", numpadDisplayValue);
    // setNumpadDisplayValue(''); // Optional: Clear numpad on enter
  };

  const handleAddItemWithNumpad = useCallback((item: MenuItem) => {
    const quantity = parseInt(numpadDisplayValue, 10);
    if (!isNaN(quantity) && quantity > 0) {
      addItemToOrder(item, quantity);
      setNumpadDisplayValue(''); // Clear Numpad after using its value
    } else {
      addItemToOrder(item); // Default behavior: add 1 or increment
    }
  }, [addItemToOrder, numpadDisplayValue]);


  return (
    <div className="flex flex-col h-screen bg-dark-surface text-light-text font-sans">
      <Header currentPage={currentPage} onNavigate={setCurrentPage} />
      <div className="flex flex-1 overflow-hidden">
        {currentPage === 'pos' && (
          <div className="flex flex-1 overflow-hidden">
            <Numpad
              displayValue={numpadDisplayValue}
              onDigitPress={handleNumpadInput}
              onClearPress={handleNumpadClear}
              onBackspacePress={handleNumpadBackspace}
              onEnterPress={handleNumpadEnter} // Enter might not be used or clear display
            />
            <main className="flex flex-1 overflow-hidden p-4 space-x-4">
              <MenuDisplay
                menuItems={filteredMenuItems}
                categories={uniqueCategories}
                selectedCategory={selectedCategory}
                onSelectCategory={setSelectedCategory}
                onAddItem={handleAddItemWithNumpad} // Use the new handler
              />
              <OrderPanel
                orderItems={orderItems}
                onRemoveItem={removeItemFromOrder}
                onUpdateQuantity={updateItemQuantity}
                onClearOrder={clearOrder}
                onProcessPayment={handlePayment}
                total={calculateTotal()}
              />
            </main>
          </div>
        )}
        {currentPage === 'sales' && <SalesView />}
        {currentPage === 'reports' && <ReportsView />}
      </div>
      {isPaymentModalOpen && orderToPayForModal && (
        <PaymentModal
          isOpen={isPaymentModalOpen}
          onClose={handleCloseModal}
          orderItems={orderToPayForModal}
          totalAmount={paymentTotalForModal}
        />
      )}
    </div>
  );
};

export default App;