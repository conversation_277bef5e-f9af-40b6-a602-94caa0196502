export interface MenuItem {
  id: string;
  name: string;
  price: number;
  category: string;
  imageUrl: string;
  description: string;
}

export interface OrderItem extends MenuItem {
  quantity: number;
}

export interface Sale {
  id: string; // Unique identifier for the sale
  timestamp: string; // ISO string of when the sale occurred
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
}

export interface SaleDetailsForStorage {
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
}

export type Page = 'pos' | 'sales' | 'reports';