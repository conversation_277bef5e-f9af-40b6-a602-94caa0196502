import Database from 'better-sqlite3';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Créer une connexion à la base de données SQLite
const db = new Database(path.join(__dirname, '../../database.sqlite'), { verbose: console.log });

// Créer les tables si elles n'existent pas
const initDb = () => {
  // Table des produits
  db.exec(`
    CREATE TABLE IF NOT EXISTS produits (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      nom TEXT NOT NULL,
      description TEXT,
      prix REAL NOT NULL,
      quantite INTEGER DEFAULT 0,
      date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Table des ventes
  db.exec(`
    CREATE TABLE IF NOT EXISTS ventes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      timestamp TEXT NOT NULL,
      subtotal REAL NOT NULL,
      tax_amount REAL NOT NULL,
      total_amount REAL NOT NULL,
      date_creation DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Table des articles de vente
  db.exec(`
    CREATE TABLE IF NOT EXISTS vente_items (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      vente_id INTEGER NOT NULL,
      item_id TEXT NOT NULL,
      item_name TEXT NOT NULL,
      price REAL NOT NULL,
      quantity INTEGER NOT NULL,
      FOREIGN KEY (vente_id) REFERENCES ventes (id) ON DELETE CASCADE
    );
  `);
};

// Exporter la connexion à la base de données et la fonction d'initialisation
export { db, initDb };
