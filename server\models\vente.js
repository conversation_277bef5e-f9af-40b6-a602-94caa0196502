import { db } from '../db/database.js';

// Créer une nouvelle vente
export const createVente = (vente) => {
  const { timestamp, subtotal, taxAmount, totalAmount } = vente;
  
  // Insérer la vente principale
  const stmt = db.prepare(
    'INSERT INTO ventes (timestamp, subtotal, tax_amount, total_amount) VALUES (?, ?, ?, ?)'
  );
  const result = stmt.run(timestamp, subtotal, taxAmount, totalAmount);
  const venteId = result.lastInsertRowid;
  
  // Insérer les articles de la vente
  const stmtItems = db.prepare(
    'INSERT INTO vente_items (vente_id, item_id, item_name, price, quantity) VALUES (?, ?, ?, ?, ?)'
  );
  
  // Utiliser une transaction pour garantir l'intégrité des données
  const insertItems = db.transaction((items) => {
    for (const item of items) {
      stmtItems.run(venteId, item.id, item.name, item.price, item.quantity);
    }
  });
  
  insertItems(vente.items);
  
  return venteId;
};

// Récupérer toutes les ventes
export const getVentes = () => {
  const stmt = db.prepare('SELECT * FROM ventes ORDER BY timestamp DESC');
  return stmt.all();
};

// Récupérer une vente par son ID avec ses articles
export const getVenteById = (id) => {
  // Récupérer la vente
  const stmtVente = db.prepare('SELECT * FROM ventes WHERE id = ?');
  const vente = stmtVente.get(id);
  
  if (!vente) return null;
  
  // Récupérer les articles de la vente
  const stmtItems = db.prepare('SELECT * FROM vente_items WHERE vente_id = ?');
  const items = stmtItems.all(id);
  
  return { ...vente, items };
};

// Récupérer les ventes pour une période donnée
export const getVentesByPeriod = (startDate, endDate) => {
  const stmt = db.prepare(
    'SELECT * FROM ventes WHERE timestamp BETWEEN ? AND ? ORDER BY timestamp DESC'
  );
  return stmt.all(startDate, endDate);
};

// Supprimer une vente
export const deleteVente = (id) => {
  // Supprimer d'abord les articles liés à la vente
  const stmtDeleteItems = db.prepare('DELETE FROM vente_items WHERE vente_id = ?');
  stmtDeleteItems.run(id);
  
  // Puis supprimer la vente elle-même
  const stmtDeleteVente = db.prepare('DELETE FROM ventes WHERE id = ?');
  return stmtDeleteVente.run(id).changes > 0;
};