import React from 'react';
import { FuturisticButton } from './FuturisticButton';

interface NumpadProps {
  displayValue: string;
  onDigitPress: (digit: string) => void;
  onClearPress: () => void;
  onBackspacePress: () => void;
  onEnterPress: () => void;
}

const BackspaceIconModern: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 12h-15m0 0l6.75-6.75M4.5 12l6.75 6.75" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M21 12H7.5" /> {/* Optional: shorter main line for different style */}
  </svg>
);


const NumpadButton: React.FC<React.ButtonHTMLAttributes<HTMLButtonElement> & {children: React.ReactNode, className?: string, isLarge?: boolean}> = ({ children, className, isLarge, ...props }) => (
  <button
    type="button"
    className={`
      ${isLarge ? 'col-span-2' : ''}
      bg-slate-700/60 hover:bg-slate-600/80 border border-slate-600 
      text-light-text text-xl sm:text-2xl font-medium 
      rounded-lg shadow-md hover:shadow-glow-secondary 
      aspect-square flex items-center justify-center 
      transition-all duration-150 ease-in-out transform hover:scale-105 active:scale-95
      focus:outline-none focus:ring-2 focus:ring-brand-primary focus:ring-opacity-75
      ${className || ''}
    `}
    {...props}
  >
    {children}
  </button>
);

export const Numpad: React.FC<NumpadProps> = ({
  displayValue,
  onDigitPress,
  onClearPress,
  onBackspacePress,
  onEnterPress // This is available but might not be actively used by the user flow for quantity
}) => {
  const digits = ['7', '8', '9', '4', '5', '6', '1', '2', '3'];

  return (
    <div className="w-60 md:w-72 bg-dark-card p-3 sm:p-4 flex flex-col space-y-2 sm:space-y-3 border-r-2 border-dark-border shadow-2xl">
      <div 
        className="bg-slate-900/70 border border-slate-700 rounded-lg p-3 h-14 sm:h-16 text-right text-2xl sm:text-3xl font-mono text-brand-primary flex items-center justify-end overflow-hidden shadow-inner"
        aria-live="polite"
        role="status"
        title="Numpad Display"
      >
        {displayValue || <span className="text-slate-600">0</span>}
      </div>
      
      <div className="grid grid-cols-3 gap-1.5 sm:gap-2">
        <NumpadButton onClick={onClearPress} className="text-red-400 hover:bg-red-500/40 text-lg sm:text-xl col-span-2">
          CLR
        </NumpadButton>
        <NumpadButton onClick={onBackspacePress} className="text-yellow-400 hover:bg-yellow-500/40">
          <BackspaceIconModern className="w-6 h-6 sm:w-7 sm:h-7" />
        </NumpadButton>

        {digits.map((digit) => (
          <NumpadButton key={digit} onClick={() => onDigitPress(digit)}>
            {digit}
          </NumpadButton>
        ))}
        
        <NumpadButton onClick={() => onDigitPress('0')} isLarge>0</NumpadButton>
        {/* Removed decimal point as it's for quantity 
        <NumpadButton onClick={() => onDigitPress('.')}>.</NumpadButton> 
        */}
        <NumpadButton onClick={onEnterPress} className="bg-brand-primary/80 hover:bg-brand-primary text-dark-bg text-lg sm:text-xl">
          ENT
        </NumpadButton>
      </div>
       <p className="text-xs text-center text-slate-500 pt-1 sm:pt-2">Enter Qty, then select item.</p>
    </div>
  );
};