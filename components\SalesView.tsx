import React, { useState, useEffect } from 'react';
import type { Sale } from '../types';
import { getSalesFromLocalStorage } from '../utils/salesStorage';
import { getSalesFromDatabase } from '../src/services/venteService';
import { FuturisticButton } from './FuturisticButton';

const EyeIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
  </svg>
);


const SaleDetailModal: React.FC<{ sale: Sale; onClose: () => void }> = ({ sale, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-subtle-slide-in">
      <div className="bg-dark-card rounded-xl shadow-2xl p-6 w-full max-w-lg border border-brand-secondary/50">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-brand-primary">Sale Details (ID: ...{sale.id.slice(-6)})</h2>
          <FuturisticButton onClick={onClose} variant="ghost" size="sm">Close</FuturisticButton>
        </div>
        <div className="text-sm text-medium-text mb-3">
          <p><strong>Date:</strong> {new Date(sale.timestamp).toLocaleString()}</p>
        </div>
        <div className="max-h-60 overflow-y-auto mb-4 pr-2 scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-slate-700 border-t border-b border-dark-border py-3">
          <h3 className="text-lg font-semibold text-light-text mb-2">Items:</h3>
          {sale.items.map(item => (
            <div key={item.id + item.name} className="flex justify-between items-center py-1.5 text-sm">
              <span className="text-medium-text">{item.name} (x{item.quantity})</span>
              <span className="text-light-text">${(item.price * item.quantity).toFixed(2)}</span>
            </div>
          ))}
        </div>
        <div className="space-y-1 text-sm border-t border-dark-border pt-3">
            <div className="flex justify-between text-medium-text"><span>Subtotal:</span><span className="text-light-text">${sale.subtotal.toFixed(2)}</span></div>
            <div className="flex justify-between text-medium-text"><span>Tax:</span><span className="text-light-text">${sale.taxAmount.toFixed(2)}</span></div>
            <div className="flex justify-between text-xl font-bold text-brand-primary mt-1"><span>Total Paid:</span><span>${sale.totalAmount.toFixed(2)}</span></div>
        </div>
      </div>
    </div>
  );
};


export const SalesView: React.FC = () => {
  const [sales, setSales] = useState<Sale[]>([]);
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);

  useEffect(() => {
    // Récupérer les ventes depuis le stockage local
    const localSales = getSalesFromLocalStorage();
    setSales(localSales);
    
    // Récupérer les ventes depuis la base de données
    getSalesFromDatabase()
      .then(dbSales => {
        if (dbSales.length > 0) {
          // Fusionner les ventes locales et celles de la base de données
          // en évitant les doublons (basés sur l'ID)
          const allSalesMap = new Map();
          
          // Ajouter d'abord les ventes locales
          localSales.forEach(sale => {
            allSalesMap.set(sale.id, sale);
          });
          
          // Ajouter ensuite les ventes de la base de données
          dbSales.forEach(sale => {
            if (!allSalesMap.has(sale.id)) {
              allSalesMap.set(sale.id, sale);
            }
          });
          
          // Convertir la Map en tableau
          const allSales = Array.from(allSalesMap.values());
          setSales(allSales);
        }
      })
      .catch(error => {
        console.error('Erreur lors de la récupération des ventes depuis la base de données:', error);
      });
  }, []);

  const formatDate = (isoString: string) => {
    return new Date(isoString).toLocaleString('en-US', { 
      year: 'numeric', month: 'short', day: 'numeric', 
      hour: '2-digit', minute: '2-digit' 
    });
  };

  if (sales.length === 0) {
    return (
      <div className="flex-grow flex flex-col items-center justify-center text-center p-8 bg-dark-surface">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-28 h-28 text-slate-700 mb-6">
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6-2.292m0 0V3.75m0 16.5A8.967 8.967 0 0 1 6 18m0 0a8.967 8.967 0 0 1-2.402-1.171M12 21a8.967 8.967 0 0 0 2.402-1.171M12 21V3.75" />
        </svg>
        <h2 className="text-2xl font-semibold text-brand-primary mb-2">No Sales Yet</h2>
        <p className="text-medium-text">Complete some transactions in the POS to see sales history here.</p>
      </div>
    );
  }
  
  return (
    <div className="flex-grow p-6 bg-dark-surface overflow-y-auto">
      <h1 className="text-3xl font-bold text-brand-primary mb-6">Sales History</h1>
      <div className="bg-dark-card shadow-xl rounded-lg overflow-hidden border border-dark-border">
        <table className="min-w-full divide-y divide-dark-border">
          <thead className="bg-slate-800">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-brand-primary uppercase tracking-wider">Sale ID</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-brand-primary uppercase tracking-wider">Date & Time</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-brand-primary uppercase tracking-wider">Items</th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-brand-primary uppercase tracking-wider">Subtotal</th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-brand-primary uppercase tracking-wider">Tax</th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-brand-primary uppercase tracking-wider">Total</th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-brand-primary uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-dark-card divide-y divide-slate-700/50">
            {sales.slice().reverse().map((sale) => ( // Show newest first
              <tr key={sale.id} className="hover:bg-slate-700/30 transition-colors duration-150">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-light-text" title={sale.id}>...{sale.id.slice(-8)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-medium-text">{formatDate(sale.timestamp)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-medium-text text-center">{sale.items.reduce((acc, item) => acc + item.quantity, 0)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-medium-text text-right">${sale.subtotal.toFixed(2)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-medium-text text-right">${sale.taxAmount.toFixed(2)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-brand-primary text-right">${sale.totalAmount.toFixed(2)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                   <FuturisticButton
                      size="sm"
                      variant="ghost"
                      onClick={() => setSelectedSale(sale)}
                      icon={<EyeIcon className="w-4 h-4" />}
                      aria-label="View Sale Details"
                   >
                     Details
                   </FuturisticButton>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {selectedSale && <SaleDetailModal sale={selectedSale} onClose={() => setSelectedSale(null)} />}
       <p className="text-xs text-slate-600 mt-4 text-center">Sales data is stored locally in your browser. Clearing browser data will remove this history.</p>
    </div>
  );
};
