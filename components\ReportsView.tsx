import React, { useEffect, useRef, useState } from 'react';
import { Chart, registerables } from 'chart.js/auto';
import type { Sale, OrderItem, MenuItem } from '../types';
import { getSalesFromLocalStorage } from '../utils/salesStorage';
import { MENU_ITEMS } from '../constants'; // To get category info

Chart.register(...registerables);

// Helper to get item details (like category) if needed
const getItemDetails = (itemId: string): Partial<MenuItem> => {
  return MENU_ITEMS.find(menuItem => menuItem.id === itemId) || {};
};

export const ReportsView: React.FC = () => {
  const [sales, setSales] = useState<Sale[]>([]);
  
  const salesOverTimeChartRef = useRef<HTMLCanvasElement>(null);
  const topItemsChartRef = useRef<HTMLCanvasElement>(null);
  const revenueByCategoryChartRef = useRef<HTMLCanvasElement>(null);

  const chartInstancesRef = useRef<{ [key: string]: Chart | null }>({});

  useEffect(() => {
    setSales(getSalesFromLocalStorage());
  }, []);

  useEffect(() => {
    const destroyCharts = () => {
      Object.values(chartInstancesRef.current).forEach(chart => chart?.destroy());
      chartInstancesRef.current = {};
    };
    destroyCharts();

    if (sales.length === 0) return;

    // --- Sales Over Time Data ---
    const salesByDate: { [key: string]: number } = sales.reduce((acc, sale) => {
      const date = new Date(sale.timestamp).toLocaleDateString('en-CA'); // YYYY-MM-DD for sorting
      acc[date] = (acc[date] || 0) + sale.totalAmount;
      return acc;
    }, {});
    const sortedDates = Object.keys(salesByDate).sort();
    const salesOverTimeLabels = sortedDates.map(date => new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric'}));
    const salesOverTimeData = sortedDates.map(date => salesByDate[date]);

    // --- Top Selling Items Data ---
    const itemCounts: { [key: string]: { name: string, quantity: number } } = {};
    sales.forEach(sale => {
      sale.items.forEach(item => {
        if (!itemCounts[item.id]) {
          itemCounts[item.id] = { name: item.name, quantity: 0 };
        }
        itemCounts[item.id].quantity += item.quantity;
      });
    });
    const sortedTopItems = Object.values(itemCounts).sort((a, b) => b.quantity - a.quantity).slice(0, 5); // Top 5
    const topItemsLabels = sortedTopItems.map(item => item.name);
    const topItemsData = sortedTopItems.map(item => item.quantity);

    // --- Revenue By Category Data ---
    const revenueByCategory: { [key: string]: number } = {};
    sales.forEach(sale => {
      sale.items.forEach(item => {
        const category = getItemDetails(item.id)?.category || 'Unknown';
        revenueByCategory[category] = (revenueByCategory[category] || 0) + (item.price * item.quantity);
      });
    });
    const revenueByCategoryLabels = Object.keys(revenueByCategory);
    const revenueByCategoryData = Object.values(revenueByCategory);
    
    // Chart Colors
    const primaryColor = 'rgba(34, 211, 238, 0.8)'; // brand-primary
    const secondaryColor = 'rgba(168, 85, 247, 0.8)'; // brand-secondary
    const accentColor = 'rgba(236, 72, 153, 0.8)'; // brand-accent
    const lightTextColor = '#f1f5f9'; // slate-100
    const gridColor = 'rgba(51, 65, 85, 0.5)'; // slate-700 with alpha


    // Create Charts
    if (salesOverTimeChartRef.current && salesOverTimeData.length > 0) {
      chartInstancesRef.current.salesOverTime = new Chart(salesOverTimeChartRef.current, {
        type: 'line',
        data: {
          labels: salesOverTimeLabels,
          datasets: [{
            label: 'Total Sales',
            data: salesOverTimeData,
            borderColor: primaryColor,
            backgroundColor: 'rgba(34, 211, 238, 0.2)',
            tension: 0.1,
            fill: true,
          }]
        },
        options: {
          responsive: true, maintainAspectRatio: false,
          scales: { 
            y: { ticks: { color: lightTextColor }, grid: { color: gridColor } },
            x: { ticks: { color: lightTextColor }, grid: { color: gridColor } }
          },
          plugins: { legend: { labels: { color: lightTextColor } } }
        }
      });
    }

    if (topItemsChartRef.current && topItemsData.length > 0) {
      chartInstancesRef.current.topItems = new Chart(topItemsChartRef.current, {
        type: 'bar',
        data: {
          labels: topItemsLabels,
          datasets: [{
            label: 'Quantity Sold',
            data: topItemsData,
            backgroundColor: [primaryColor, secondaryColor, accentColor, '#facc15', '#4ade80'], // yellow, green
            borderColor: 'rgba(255,255,255,0.1)',
            borderWidth: 1
          }]
        },
        options: {
          indexAxis: 'y', responsive: true, maintainAspectRatio: false,
           scales: { 
            y: { ticks: { color: lightTextColor }, grid: { color: gridColor } },
            x: { ticks: { color: lightTextColor }, grid: { color: gridColor } }
          },
          plugins: { legend: { display: false } } // Or set labels.color if displayed
        }
      });
    }

    if (revenueByCategoryChartRef.current && revenueByCategoryData.length > 0) {
      chartInstancesRef.current.revenueByCategory = new Chart(revenueByCategoryChartRef.current, {
        type: 'doughnut',
        data: {
          labels: revenueByCategoryLabels,
          datasets: [{
            label: 'Revenue',
            data: revenueByCategoryData,
            backgroundColor: [primaryColor, secondaryColor, accentColor, '#f97316', '#10b981', '#6366f1'], // orange, emerald, indigo
            hoverOffset: 4
          }]
        },
        options: {
          responsive: true, maintainAspectRatio: false,
          plugins: { legend: { position: 'top', labels: { color: lightTextColor } } }
        }
      });
    }
    return () => destroyCharts();
  }, [sales]);


  if (sales.length === 0) {
    return (
      <div className="flex-grow flex flex-col items-center justify-center text-center p-8 bg-dark-surface">
         <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-28 h-28 text-slate-700 mb-6">
          <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h12M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-1.5m-6-3.75h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008Zm0-4.5h.008v.008H9.75v-.008Zm2.25-4.5h.008v.008H12V10.5Zm2.25 2.25h.008V15H14.25v-.008Zm2.25-2.25h.008v.008H16.5v-.008Z" />
        </svg>
        <h2 className="text-2xl font-semibold text-brand-primary mb-2">No Sales Data for Reports</h2>
        <p className="text-medium-text">Complete some transactions to generate reports.</p>
      </div>
    );
  }

  return (
    <div className="flex-grow p-4 md:p-6 bg-dark-surface overflow-y-auto scrollbar-thin">
      <h1 className="text-3xl font-bold text-brand-primary mb-6">Sales Reports</h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        <div className="bg-dark-card p-4 rounded-lg shadow-xl border border-dark-border">
          <h2 className="text-xl font-semibold text-brand-secondary mb-3">Sales Over Time</h2>
          <div className="chart-container min-h-[300px] md:min-h-[350px]">
            <canvas ref={salesOverTimeChartRef}></canvas>
          </div>
        </div>

        <div className="bg-dark-card p-4 rounded-lg shadow-xl border border-dark-border">
          <h2 className="text-xl font-semibold text-brand-secondary mb-3">Top Selling Items (Top 5)</h2>
          <div className="chart-container min-h-[300px] md:min-h-[350px]">
            <canvas ref={topItemsChartRef}></canvas>
          </div>
        </div>

        <div className="bg-dark-card p-4 rounded-lg shadow-xl border border-dark-border lg:col-span-2">
          <h2 className="text-xl font-semibold text-brand-secondary mb-3">Revenue by Category</h2>
           <div className="chart-container min-h-[300px] md:min-h-[350px] max-w-md mx-auto"> {/* Centered and smaller for doughnut */}
            <canvas ref={revenueByCategoryChartRef}></canvas>
          </div>
        </div>
      </div>
      <p className="text-xs text-slate-600 mt-6 text-center">Report data is based on locally stored sales. Clearing browser data will reset reports.</p>
    </div>
  );
};