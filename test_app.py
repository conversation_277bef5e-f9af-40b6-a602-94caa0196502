#!/usr/bin/env python3
"""
Tests pour l'application QuantumServe POS
"""

import unittest
import json
import tempfile
import os
from app import create_app
from models import db, Product, Sale, SaleItem

class POSTestCase(unittest.TestCase):
    """Tests pour l'application POS"""

    def setUp(self):
        """Configuration avant chaque test"""
        self.db_fd, self.db_path = tempfile.mkstemp()
        self.app = create_app('testing')
        self.app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{self.db_path}'
        self.app.config['TESTING'] = True
        
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
            self.create_test_products()

    def tearDown(self):
        """Nettoyage après chaque test"""
        os.close(self.db_fd)
        os.unlink(self.db_path)

    def create_test_products(self):
        """Créer des produits de test"""
        test_products = [
            {
                'id': 'test1',
                'name': 'Test Burger',
                'price': 10.99,
                'category': 'Test Category',
                'image_url': 'https://example.com/test.jpg',
                'description': 'Un burger de test'
            },
            {
                'id': 'test2',
                'name': 'Test Drink',
                'price': 2.99,
                'category': 'Drinks',
                'image_url': 'https://example.com/drink.jpg',
                'description': 'Une boisson de test'
            }
        ]
        
        for product_data in test_products:
            product = Product(**product_data)
            db.session.add(product)
        
        db.session.commit()

    def test_index_page(self):
        """Test de la page d'accueil"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'QuantumServe POS', response.data)

    def test_get_products(self):
        """Test de récupération des produits"""
        response = self.client.get('/api/products')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIsInstance(data, list)
        self.assertGreaterEqual(len(data), 2)  # Au moins nos 2 produits de test
        
        # Vérifier la structure d'un produit
        product = data[0]
        required_fields = ['id', 'name', 'price', 'category', 'imageUrl', 'description']
        for field in required_fields:
            self.assertIn(field, product)

    def test_create_product(self):
        """Test de création d'un produit"""
        new_product = {
            'id': 'test3',
            'name': 'Test Pizza',
            'price': 15.99,
            'category': 'Main Course',
            'imageUrl': 'https://example.com/pizza.jpg',
            'description': 'Une pizza de test'
        }
        
        response = self.client.post('/api/products', 
                                  data=json.dumps(new_product),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 201)
        
        # Vérifier que le produit a été créé
        response = self.client.get('/api/products')
        data = json.loads(response.data)
        product_ids = [p['id'] for p in data]
        self.assertIn('test3', product_ids)

    def test_get_sales_empty(self):
        """Test de récupération des ventes (liste vide)"""
        response = self.client.get('/api/sales')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 0)

    def test_create_sale(self):
        """Test de création d'une vente"""
        sale_data = {
            'id': 'test_sale_123',
            'timestamp': '2024-01-01T12:00:00.000Z',
            'subtotal': 13.98,
            'taxAmount': 1.12,
            'totalAmount': 15.10,
            'items': [
                {
                    'id': 'test1',
                    'name': 'Test Burger',
                    'price': 10.99,
                    'quantity': 1,
                    'category': 'Test Category'
                },
                {
                    'id': 'test2',
                    'name': 'Test Drink',
                    'price': 2.99,
                    'quantity': 1,
                    'category': 'Drinks'
                }
            ]
        }
        
        response = self.client.post('/api/sales',
                                  data=json.dumps(sale_data),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 201)
        
        response_data = json.loads(response.data)
        self.assertTrue(response_data['success'])
        self.assertIn('message', response_data)

    def test_get_sales_with_data(self):
        """Test de récupération des ventes avec données"""
        # Créer d'abord une vente
        self.test_create_sale()
        
        # Récupérer les ventes
        response = self.client.get('/api/sales')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 1)
        
        # Vérifier la structure de la vente
        sale = data[0]
        required_fields = ['id', 'timestamp', 'subtotal', 'taxAmount', 'totalAmount', 'items']
        for field in required_fields:
            self.assertIn(field, sale)
        
        # Vérifier les items
        self.assertEqual(len(sale['items']), 2)
        item = sale['items'][0]
        item_fields = ['id', 'name', 'price', 'quantity', 'category']
        for field in item_fields:
            self.assertIn(field, item)

    def test_sale_calculation(self):
        """Test des calculs de vente"""
        # Créer une vente avec des calculs spécifiques
        sale_data = {
            'id': 'test_calc_sale',
            'timestamp': '2024-01-01T12:00:00.000Z',
            'subtotal': 100.00,
            'taxAmount': 8.00,  # 8% de TVA
            'totalAmount': 108.00,
            'items': [
                {
                    'id': 'test1',
                    'name': 'Test Item',
                    'price': 50.00,
                    'quantity': 2,
                    'category': 'Test'
                }
            ]
        }
        
        response = self.client.post('/api/sales',
                                  data=json.dumps(sale_data),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 201)
        
        # Vérifier que les données sont correctement stockées
        response = self.client.get('/api/sales')
        data = json.loads(response.data)
        sale = data[0]
        
        self.assertEqual(sale['subtotal'], 100.00)
        self.assertEqual(sale['taxAmount'], 8.00)
        self.assertEqual(sale['totalAmount'], 108.00)

if __name__ == '__main__':
    # Ajouter la configuration de test à config.py si elle n'existe pas
    import config
    if not hasattr(config, 'TestingConfig'):
        class TestingConfig(config.Config):
            TESTING = True
            SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
        
        config.config['testing'] = TestingConfig
    
    unittest.main()
