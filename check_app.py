#!/usr/bin/env python3
"""
Script simple pour vérifier que l'application fonctionne
"""

import requests
import json
import time

def test_application():
    """Teste les fonctionnalités principales de l'application"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Test de l'application QuantumServe POS")
    print("=" * 50)
    
    try:
        # Test 1: Page d'accueil
        print("1. Test de la page d'accueil...")
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print("   ✅ Page d'accueil accessible")
        else:
            print(f"   ❌ Erreur page d'accueil: {response.status_code}")
            return False
        
        # Test 2: API Produits
        print("2. Test de l'API produits...")
        response = requests.get(f"{base_url}/api/products")
        if response.status_code == 200:
            products = response.json()
            print(f"   ✅ API produits accessible - {len(products)} produits trouvés")
            if len(products) > 0:
                print(f"   📦 Premier produit: {products[0]['name']} - {products[0]['price']}€")
        else:
            print(f"   ❌ Erreur API produits: {response.status_code}")
            return False
        
        # Test 3: API Ventes (lecture)
        print("3. Test de l'API ventes (lecture)...")
        response = requests.get(f"{base_url}/api/sales")
        if response.status_code == 200:
            sales = response.json()
            print(f"   ✅ API ventes accessible - {len(sales)} ventes trouvées")
        else:
            print(f"   ❌ Erreur API ventes: {response.status_code}")
            return False
        
        # Test 4: Création d'une vente de test
        print("4. Test de création d'une vente...")
        test_sale = {
            "id": f"test_sale_{int(time.time())}",
            "timestamp": "2024-01-01T12:00:00.000Z",
            "subtotal": 16.49,
            "taxAmount": 1.32,
            "totalAmount": 17.81,
            "items": [
                {
                    "id": "1",
                    "name": "Photon Burger",
                    "price": 12.99,
                    "quantity": 1,
                    "category": "Main Course"
                },
                {
                    "id": "2",
                    "name": "Cosmic Fries",
                    "price": 4.50,
                    "quantity": 1,
                    "category": "Sides"
                }
            ]
        }
        
        response = requests.post(f"{base_url}/api/sales", 
                               json=test_sale,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 201:
            result = response.json()
            print(f"   ✅ Vente créée avec succès: {result['message']}")
        else:
            print(f"   ❌ Erreur création vente: {response.status_code}")
            print(f"   📝 Réponse: {response.text}")
            return False
        
        # Test 5: Vérification de la vente créée
        print("5. Vérification de la vente créée...")
        response = requests.get(f"{base_url}/api/sales")
        if response.status_code == 200:
            sales = response.json()
            test_sale_found = any(sale['id'] == test_sale['id'] for sale in sales)
            if test_sale_found:
                print("   ✅ Vente de test trouvée dans la base de données")
            else:
                print("   ⚠️  Vente de test non trouvée (peut-être un problème de synchronisation)")
        
        print("\n🎉 Tous les tests sont passés avec succès!")
        print("\n📊 Résumé des fonctionnalités testées:")
        print("   • Interface web accessible")
        print("   • API REST fonctionnelle")
        print("   • Base de données opérationnelle")
        print("   • Création et lecture des ventes")
        print("   • Gestion des produits")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter à l'application")
        print("   Assurez-vous que l'application est démarrée avec: python app.py")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def show_usage_instructions():
    """Affiche les instructions d'utilisation"""
    print("\n📖 Instructions d'utilisation:")
    print("=" * 50)
    print("1. 🌐 Ouvrez votre navigateur")
    print("2. 🔗 Allez à: http://127.0.0.1:5000")
    print("3. 🎨 Testez le bouton de changement de thème (en haut à droite)")
    print("4. 🛒 Ajoutez des produits au panier")
    print("5. 🔢 Utilisez le pavé numérique pour les quantités")
    print("6. 💳 Effectuez un paiement")
    print("7. 📊 Consultez les ventes et rapports")
    print("\n🎯 Fonctionnalités clés:")
    print("   • Thème futuriste/classique")
    print("   • Interface POS complète")
    print("   • Gestion des ventes")
    print("   • Rapports avec graphiques")
    print("   • Stockage local + serveur")

if __name__ == "__main__":
    success = test_application()
    
    if success:
        show_usage_instructions()
    else:
        print("\n🔧 Dépannage:")
        print("1. Vérifiez que l'environnement virtuel est activé")
        print("2. Vérifiez que Flask est installé: pip install -r requirements.txt")
        print("3. Démarrez l'application: python app.py")
        print("4. Attendez que le serveur soit prêt avant de relancer ce test")
