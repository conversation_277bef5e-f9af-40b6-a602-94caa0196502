{"name": "pos_futurist_tsx", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server/index.js", "dev:full": "concurrently -n VITE,EXPRESS -c yellow,blue \"npm run dev\" \"npm run server\"", "start": "node server/index.js"}, "engines": {"node": ">=14.0.0"}, "dependencies": {"better-sqlite3": "^9.4.3", "body-parser": "^1.20.2", "chart.js": "^4.4.9", "cors": "^2.8.5", "express": "^4.18.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.12", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "concurrently": "^8.2.2", "nodemon": "^3.1.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}