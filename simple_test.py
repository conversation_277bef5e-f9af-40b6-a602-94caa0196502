#!/usr/bin/env python3
"""
Test simple pour vérifier la structure de l'application
"""

import os
import sys

def check_file_structure():
    """Vérifie que tous les fichiers nécessaires sont présents"""
    print("🔍 Vérification de la structure des fichiers...")
    print("=" * 50)
    
    required_files = [
        'app.py',
        'models.py', 
        'config.py',
        'requirements.txt',
        'templates/index.html',
        'static/css/themes.css',
        'static/js/app.js',
        'static/js/api.js',
        'static/js/pos.js',
        'static/js/sales.js',
        'static/js/reports.js',
        'static/js/themes.js'
    ]
    
    missing_files = []
    present_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            present_files.append(file_path)
            print(f"   ✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"   ❌ {file_path} - MANQUANT")
    
    print(f"\n📊 Résumé: {len(present_files)}/{len(required_files)} fichiers présents")
    
    if missing_files:
        print(f"\n⚠️  Fichiers manquants: {len(missing_files)}")
        for file in missing_files:
            print(f"   • {file}")
        return False
    else:
        print("\n🎉 Tous les fichiers requis sont présents!")
        return True

def check_python_syntax():
    """Vérifie la syntaxe Python des fichiers principaux"""
    print("\n🐍 Vérification de la syntaxe Python...")
    print("=" * 50)
    
    python_files = ['app.py', 'models.py', 'config.py']
    
    for file_path in python_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Compilation pour vérifier la syntaxe
                compile(content, file_path, 'exec')
                print(f"   ✅ {file_path} - Syntaxe correcte")
            except SyntaxError as e:
                print(f"   ❌ {file_path} - Erreur de syntaxe: {e}")
                return False
            except Exception as e:
                print(f"   ⚠️  {file_path} - Erreur: {e}")
        else:
            print(f"   ❌ {file_path} - Fichier non trouvé")
            return False
    
    print("\n🎉 Syntaxe Python correcte pour tous les fichiers!")
    return True

def check_html_structure():
    """Vérifie la structure HTML de base"""
    print("\n🌐 Vérification du template HTML...")
    print("=" * 50)
    
    html_file = 'templates/index.html'
    
    if not os.path.exists(html_file):
        print(f"   ❌ {html_file} non trouvé")
        return False
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifications de base
        checks = [
            ('<!DOCTYPE html>', 'Déclaration DOCTYPE'),
            ('<html', 'Balise HTML'),
            ('<head>', 'Section HEAD'),
            ('<body', 'Section BODY'),
            ('QuantumServe POS', 'Titre de l\'application'),
            ('tailwindcss.com', 'Tailwind CSS'),
            ('chart.js', 'Chart.js'),
            ('theme-toggle', 'Bouton de thème'),
            ('pos-page', 'Page POS'),
            ('sales-page', 'Page Ventes'),
            ('reports-page', 'Page Rapports')
        ]
        
        for check, description in checks:
            if check in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - Non trouvé")
        
        print("\n🎉 Structure HTML vérifiée!")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur lors de la lecture du HTML: {e}")
        return False

def check_css_structure():
    """Vérifie la structure CSS"""
    print("\n🎨 Vérification des styles CSS...")
    print("=" * 50)
    
    css_file = 'static/css/themes.css'
    
    if not os.path.exists(css_file):
        print(f"   ❌ {css_file} non trouvé")
        return False
    
    try:
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifications des thèmes
        theme_checks = [
            ('.futuristic-theme', 'Thème futuriste'),
            ('.classic-theme', 'Thème classique'),
            ('--bg-primary', 'Variables CSS'),
            ('.nav-btn', 'Boutons de navigation'),
            ('.product-card', 'Cartes de produits'),
            ('.numpad-btn', 'Boutons pavé numérique')
        ]
        
        for check, description in theme_checks:
            if check in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - Non trouvé")
        
        print("\n🎉 Styles CSS vérifiés!")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur lors de la lecture du CSS: {e}")
        return False

def show_next_steps():
    """Affiche les prochaines étapes"""
    print("\n🚀 Prochaines étapes:")
    print("=" * 50)
    print("1. 📦 Activez votre environnement virtuel:")
    print("   vv_futurist_01\\Scripts\\activate")
    print("\n2. 🔧 Installez les dépendances (si pas déjà fait):")
    print("   pip install -r requirements.txt")
    print("\n3. 🌟 Démarrez l'application:")
    print("   python app.py")
    print("\n4. 🌐 Ouvrez votre navigateur:")
    print("   http://127.0.0.1:5000")
    print("\n5. 🎨 Testez les fonctionnalités:")
    print("   • Changement de thème (bouton en haut à droite)")
    print("   • Ajout de produits au panier")
    print("   • Utilisation du pavé numérique")
    print("   • Traitement des paiements")
    print("   • Consultation des ventes et rapports")

def main():
    """Fonction principale"""
    print("🧪 Test de l'application QuantumServe POS")
    print("=" * 60)
    print("Application POS moderne avec Flask + JavaScript + Tailwind CSS")
    print("Thèmes: Futuriste (sombre) ↔ Classique (clair)")
    print("=" * 60)
    
    all_good = True
    
    # Tests
    if not check_file_structure():
        all_good = False
    
    if not check_python_syntax():
        all_good = False
    
    if not check_html_structure():
        all_good = False
    
    if not check_css_structure():
        all_good = False
    
    # Résumé
    print("\n" + "=" * 60)
    if all_good:
        print("🎉 SUCCÈS: L'application est correctement configurée!")
        print("✨ Conversion React → Flask terminée avec succès")
        print("🎨 Système de thèmes futuriste/classique opérationnel")
        show_next_steps()
    else:
        print("⚠️  ATTENTION: Certains problèmes ont été détectés")
        print("🔧 Veuillez corriger les erreurs avant de continuer")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
