<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Thèmes - QuantumServe POS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Variables CSS globales pour les thèmes */
        :root {
            /* Thème Futuriste (par défaut) */
            --bg-primary: #020617;
            --bg-secondary: #1e293b;
            --bg-card: #1e293b;
            --text-primary: #f1f5f9;
            --text-secondary: #94a3b8;
            --border-color: #334155;
            --brand-primary: #22d3ee;
            --brand-secondary: #a855f7;
            --brand-accent: #ec4899;
            --shadow-color: rgba(34, 211, 238, 0.4);
            --hover-shadow: 0 0 20px 3px rgba(34, 211, 238, 0.3);
        }

        /* Thème Classique */
        .classic-theme {
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-card: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --brand-primary: #3b82f6;
            --brand-secondary: #6366f1;
            --brand-accent: #8b5cf6;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        /* Styles de base */
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .card {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--brand-primary);
            color: var(--bg-primary);
            border: 1px solid var(--brand-primary);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--brand-secondary);
            transform: scale(1.05);
        }

        .text-brand {
            color: var(--brand-primary);
        }

        .text-secondary {
            color: var(--text-secondary);
        }
    </style>
</head>
<body class="min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <div class="card rounded-lg p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-brand">Test des Thèmes</h1>
                <button id="theme-toggle" class="btn-primary px-4 py-2 rounded-lg font-medium">
                    <span id="theme-icon">🌙</span>
                    <span id="theme-text">Classique</span>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Carte de test 1 -->
                <div class="card rounded-lg p-4">
                    <h3 class="text-xl font-semibold text-brand mb-3">Produit Test</h3>
                    <p class="text-secondary mb-4">Description du produit avec du texte secondaire</p>
                    <div class="flex justify-between items-center">
                        <span class="text-2xl font-bold text-brand">12,99 €</span>
                        <button class="btn-primary px-3 py-1 rounded text-sm">Ajouter</button>
                    </div>
                </div>

                <!-- Carte de test 2 -->
                <div class="card rounded-lg p-4">
                    <h3 class="text-xl font-semibold text-brand mb-3">Autre Produit</h3>
                    <p class="text-secondary mb-4">Autre description avec texte secondaire</p>
                    <div class="flex justify-between items-center">
                        <span class="text-2xl font-bold text-brand">8,50 €</span>
                        <button class="btn-primary px-3 py-1 rounded text-sm">Ajouter</button>
                    </div>
                </div>
            </div>

            <div class="mt-6">
                <h2 class="text-2xl font-semibold text-brand mb-4">Test des couleurs</h2>
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto mb-2 rounded-lg" style="background-color: var(--brand-primary);"></div>
                        <p class="text-sm text-secondary">Primary</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto mb-2 rounded-lg" style="background-color: var(--brand-secondary);"></div>
                        <p class="text-sm text-secondary">Secondary</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto mb-2 rounded-lg" style="background-color: var(--brand-accent);"></div>
                        <p class="text-sm text-secondary">Accent</p>
                    </div>
                </div>
            </div>

            <div class="mt-6">
                <h2 class="text-2xl font-semibold text-brand mb-4">État actuel</h2>
                <div class="card rounded-lg p-4">
                    <p><strong>Thème actuel:</strong> <span id="current-theme">Futuriste</span></p>
                    <p><strong>Couleur primaire:</strong> <span id="primary-color">--</span></p>
                    <p><strong>Couleur de fond:</strong> <span id="bg-color">--</span></p>
                    <p><strong>Couleur du texte:</strong> <span id="text-color">--</span></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ThemeTest {
            constructor() {
                this.currentTheme = 'futuristic';
                this.init();
            }

            init() {
                this.setupThemeToggle();
                this.updateDisplay();
            }

            setupThemeToggle() {
                const toggleBtn = document.getElementById('theme-toggle');
                toggleBtn.addEventListener('click', () => {
                    this.toggleTheme();
                });
            }

            toggleTheme() {
                this.currentTheme = this.currentTheme === 'futuristic' ? 'classic' : 'futuristic';
                this.applyTheme(this.currentTheme);
            }

            applyTheme(theme) {
                const body = document.body;
                const themeIcon = document.getElementById('theme-icon');
                const themeText = document.getElementById('theme-text');

                // Supprimer les classes existantes
                body.classList.remove('futuristic-theme', 'classic-theme');

                if (theme === 'classic') {
                    body.classList.add('classic-theme');
                    themeIcon.textContent = '🌟';
                    themeText.textContent = 'Futuriste';
                } else {
                    body.classList.add('futuristic-theme');
                    themeIcon.textContent = '🌙';
                    themeText.textContent = 'Classique';
                }

                this.updateDisplay();
                console.log(`Thème changé vers: ${theme}`);
            }

            updateDisplay() {
                const currentThemeEl = document.getElementById('current-theme');
                const primaryColorEl = document.getElementById('primary-color');
                const bgColorEl = document.getElementById('bg-color');
                const textColorEl = document.getElementById('text-color');

                currentThemeEl.textContent = this.currentTheme === 'classic' ? 'Classique' : 'Futuriste';

                // Obtenir les valeurs CSS calculées
                const computedStyle = getComputedStyle(document.documentElement);
                primaryColorEl.textContent = computedStyle.getPropertyValue('--brand-primary').trim();
                bgColorEl.textContent = computedStyle.getPropertyValue('--bg-primary').trim();
                textColorEl.textContent = computedStyle.getPropertyValue('--text-primary').trim();
            }
        }

        // Initialiser le test
        const themeTest = new ThemeTest();
    </script>
</body>
</html>
