import React from 'react';
import type { Page } from '../types';

interface HeaderProps {
  currentPage: Page;
  onNavigate: (page: Page) => void;
}

export const Header: React.FC<HeaderProps> = ({ currentPage, onNavigate }) => {
  const navButtonClass = (page: Page, current: Page) => 
    `px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 transform hover:scale-105 
     ${current === page 
       ? 'bg-brand-primary text-dark-bg shadow-glow-primary' 
       : 'bg-slate-700 text-slate-300 hover:bg-slate-600 hover:text-brand-primary'}`;

  return (
    <header className="bg-dark-card p-4 shadow-lg flex items-center justify-between border-b-2 border-brand-primary/50">
      <div className="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-10 h-10 text-brand-primary mr-3">
          <path strokeLinecap="round" strokeLinejoin="round" d="M9 3.75H6.912a2.25 2.25 0 0 0-2.15 1.588L2.35 13.177a2.25 2.25 0 0 0-.1.661V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18v-4.162c0-.224-.034-.447-.1-.661L19.24 5.338A2.25 2.25 0 0 0 17.088 3.75H15M9 3.75V2.25A2.25 2.25 0 0 0 6.75 0H7.5M9 3.75S10.5 2.25 12 2.25s3 1.5 3 1.5M3 13.5h18M3 13.5a2.25 2.25 0 0 1-2.25-2.25V9.75A2.25 2.25 0 0 1 3 7.5h18a2.25 2.25 0 0 1 2.25 2.25v1.5A2.25 2.25 0 0 1 18 13.5M3 13.5V18M18 13.5V18" />
        </svg>
        <h1 className="text-3xl font-bold tracking-wider">
          <span className="text-brand-primary">Quantum</span><span className="text-light-text">Serve</span>
        </h1>
      </div>
      
      <nav className="flex items-center space-x-3">
        <button 
          onClick={() => onNavigate('pos')}
          className={navButtonClass('pos', currentPage)}
          aria-current={currentPage === 'pos' ? 'page' : undefined}
        >
          Point of Sale
        </button>
        <button 
          onClick={() => onNavigate('sales')}
          className={navButtonClass('sales', currentPage)}
          aria-current={currentPage === 'sales' ? 'page' : undefined}
        >
          Sales History
        </button>
         <button 
          onClick={() => onNavigate('reports')}
          className={navButtonClass('reports', currentPage)}
          aria-current={currentPage === 'reports' ? 'page' : undefined}
        >
          Reports
        </button>
      </nav>

      <div className="text-sm text-medium-text">
        {new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
      </div>
    </header>
  );
};