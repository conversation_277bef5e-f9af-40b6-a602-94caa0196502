// Gestionnaire de thèmes
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('pos-theme') || 'futuristic';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupThemeToggle();
    }

    applyTheme(theme) {
        const body = document.getElementById('app-body') || document.body;
        const themeIcon = document.getElementById('theme-icon');
        const themeText = document.getElementById('theme-text');

        // Supprimer les classes de thème existantes
        body.classList.remove('futuristic-theme', 'classic-theme');
        document.documentElement.classList.remove('futuristic-theme', 'classic-theme');

        if (theme === 'classic') {
            body.classList.add('classic-theme');
            document.documentElement.classList.add('classic-theme');
            if (themeIcon) themeIcon.textContent = '🌟';
            if (themeText) themeText.textContent = 'Futuriste';
            console.log('Thème classique appliqué');
        } else {
            body.classList.add('futuristic-theme');
            document.documentElement.classList.add('futuristic-theme');
            if (themeIcon) themeIcon.textContent = '🌙';
            if (themeText) themeText.textContent = 'Classique';
            console.log('Thème futuriste appliqué');
        }

        this.currentTheme = theme;
        localStorage.setItem('pos-theme', theme);

        // Forcer le recalcul des styles
        this.forceStyleRecalculation();

        // Déclencher un événement personnalisé pour notifier le changement de thème
        document.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    forceStyleRecalculation() {
        // Forcer le navigateur à recalculer les styles de manière plus douce
        document.body.offsetHeight; // Trigger reflow

        // Mettre à jour les éléments clés
        const keyElements = document.querySelectorAll('.bg-dark-surface, .bg-dark-card, .text-light-text, .text-brand-primary');
        keyElements.forEach(el => {
            el.style.transition = 'none';
            el.offsetHeight; // Trigger reflow
            el.style.transition = '';
        });
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'futuristic' ? 'classic' : 'futuristic';
        this.applyTheme(newTheme);
    }

    setupThemeToggle() {
        const themeToggleBtn = document.getElementById('theme-toggle');
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    getCurrentTheme() {
        return this.currentTheme;
    }

    // Méthodes utilitaires pour obtenir les couleurs du thème actuel
    getThemeColors() {
        if (this.currentTheme === 'classic') {
            return {
                primary: '#3b82f6',
                secondary: '#6366f1',
                accent: '#8b5cf6',
                background: '#f8fafc',
                card: '#ffffff',
                text: '#1e293b',
                textSecondary: '#64748b',
                border: '#e2e8f0'
            };
        } else {
            return {
                primary: '#22d3ee',
                secondary: '#a855f7',
                accent: '#ec4899',
                background: '#020617',
                card: '#1e293b',
                text: '#f1f5f9',
                textSecondary: '#94a3b8',
                border: '#334155'
            };
        }
    }

    // Méthode pour obtenir les couleurs Chart.js selon le thème
    getChartColors() {
        const colors = this.getThemeColors();

        if (this.currentTheme === 'classic') {
            return {
                primary: colors.primary,
                secondary: colors.secondary,
                accent: colors.accent,
                text: colors.text,
                grid: '#e2e8f0',
                background: 'rgba(59, 130, 246, 0.1)',
                datasets: [
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(99, 102, 241, 0.8)',
                    'rgba(139, 92, 246, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ]
            };
        } else {
            return {
                primary: colors.primary,
                secondary: colors.secondary,
                accent: colors.accent,
                text: colors.text,
                grid: '#334155',
                background: 'rgba(34, 211, 238, 0.1)',
                datasets: [
                    'rgba(34, 211, 238, 0.8)',
                    'rgba(168, 85, 247, 0.8)',
                    'rgba(236, 72, 153, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ]
            };
        }
    }
}

// Initialiser le gestionnaire de thèmes
const themeManager = new ThemeManager();

// Exporter pour utilisation dans d'autres modules
window.themeManager = themeManager;
