import express from 'express';
import { 
  createVente, 
  getVentes, 
  getVenteById, 
  getVentesByPeriod,
  deleteVente 
} from '../models/vente.js';

const router = express.Router();

// Créer une nouvelle vente
router.post('/', (req, res) => {
  try {
    const id = createVente(req.body);
    res.status(201).json({ id, message: 'Vente enregistrée avec succès' });
  } catch (error) {
    console.error('Erreur lors de la création de la vente:', error);
    res.status(500).json({ error: error.message });
  }
});

// Récupérer toutes les ventes
router.get('/', (req, res) => {
  try {
    const { start_date, end_date } = req.query;
    
    // Si des dates sont fournies, filtrer par période
    if (start_date && end_date) {
      const ventes = getVentesByPeriod(start_date, end_date);
      return res.json(ventes);
    }
    
    // Sinon, récupérer toutes les ventes
    const ventes = getVentes();
    res.json(ventes);
  } catch (error) {
    console.error('Erreur lors de la récupération des ventes:', error);
    res.status(500).json({ error: error.message });
  }
});

// Récupérer une vente par son ID
router.get('/:id', (req, res) => {
  try {
    const vente = getVenteById(req.params.id);
    if (vente) {
      res.json(vente);
    } else {
      res.status(404).json({ error: 'Vente non trouvée' });
    }
  } catch (error) {
    console.error('Erreur lors de la récupération de la vente:', error);
    res.status(500).json({ error: error.message });
  }
});

// Supprimer une vente
router.delete('/:id', (req, res) => {
  try {
    const deleted = deleteVente(req.params.id);
    if (deleted) {
      res.status(200).json({ message: 'Vente supprimée avec succès' });
    } else {
      res.status(404).json({ error: 'Vente non trouvée' });
    }
  } catch (error) {
    console.error('Erreur lors de la suppression de la vente:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;