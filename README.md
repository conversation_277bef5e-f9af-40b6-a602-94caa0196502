# QuantumServe POS - Système de Caisse Futuriste

Application de point de vente (POS) moderne développée avec **Python Flask**, **SQLAlchemy**, **SQLite**, **JavaScript vanilla**, **HTML** et **Tailwind CSS**.

## ✨ Fonctionnalités

### 🎯 Interface POS
- **Interface futuriste moderne** avec thème sombre et effets lumineux
- **Système de thèmes** : Basculer entre style futuriste et classique
- **Pavé numérique intégré** pour saisie rapide des quantités
- **Gestion des commandes** en temps réel
- **Calcul automatique** des taxes et totaux

### 📊 Gestion des Ventes
- **Historique complet** des ventes avec détails
- **Groupement par date** avec statistiques quotidiennes
- **Stockage local** et synchronisation serveur
- **Export des données** en CSV

### 📈 Rapports et Analytics
- **Graphiques interactifs** avec Chart.js
- **Ventes dans le temps** (graphique linéaire)
- **Articles les plus vendus** (graphique en barres)
- **Revenus par catégorie** (graphique en secteurs)
- **Adaptation automatique** aux thèmes

### 🎨 Design et UX
- **Design futuriste** avec couleurs cyan, violet et rose
- **Thème classique** avec couleurs bleues professionnelles
- **Animations fluides** et effets de transition
- **Interface responsive** pour tous les écrans
- **Raccourcis clavier** (Ctrl+1/2/3 pour navigation)

## 🛠 Stack Technologique

- **Backend** : Python Flask + SQLAlchemy
- **Base de données** : SQLite
- **Frontend** : JavaScript vanilla + HTML + Tailwind CSS
- **Graphiques** : Chart.js
- **Styles** : CSS personnalisé avec variables de thèmes

## 📋 Prérequis

- Python 3.8+
- pip (gestionnaire de paquets Python)

## 🚀 Installation

1. **Cloner le dépôt** :
   ```bash
   git clone [URL_DU_DEPOT]
   cd pos_2_mode
   ```

2. **Créer un environnement virtuel** :
   ```bash
   python -m venv vv_futurist_01
   ```

3. **Activer l'environnement virtuel** :
   ```bash
   # Windows
   vv_futurist_01\Scripts\activate

   # Linux/Mac
   source vv_futurist_01/bin/activate
   ```

4. **Installer les dépendances** :
   ```bash
   pip install -r requirements.txt
   ```

## 🎮 Démarrage

1. **Lancer l'application** :
   ```bash
   python app.py
   ```

2. **Accéder à l'application** :
   - Ouvrir votre navigateur
   - Aller à `http://127.0.0.1:5000`

## 📁 Structure du Projet

```
pos_2_mode/
├── app.py                 # Application Flask principale
├── models.py              # Modèles SQLAlchemy
├── config.py              # Configuration de l'application
├── requirements.txt       # Dépendances Python
├── templates/
│   └── index.html        # Template HTML principal
├── static/
│   ├── css/
│   │   └── themes.css    # Styles des thèmes
│   └── js/
│       ├── app.js        # Application principale
│       ├── api.js        # Service API
│       ├── pos.js        # Module POS
│       ├── sales.js      # Module ventes
│       ├── reports.js    # Module rapports
│       └── themes.js     # Gestionnaire de thèmes
└── pos_quantum.db        # Base de données SQLite (créée automatiquement)
```

## 🔌 API Endpoints

### Produits
- `GET /api/products` - Récupérer tous les produits
- `POST /api/products` - Créer un nouveau produit

### Ventes
- `GET /api/sales` - Récupérer toutes les ventes
- `POST /api/sales` - Enregistrer une nouvelle vente

## 🎨 Système de Thèmes

L'application propose deux thèmes :

### 🌟 Thème Futuriste (par défaut)
- **Couleurs** : Cyan (#22d3ee), Violet (#a855f7), Rose (#ec4899)
- **Arrière-plan** : Sombre avec effets de lueur
- **Style** : Moderne, spatial, high-tech

### 🏢 Thème Classique
- **Couleurs** : Bleu (#3b82f6), Indigo (#6366f1), Violet (#8b5cf6)
- **Arrière-plan** : Clair et professionnel
- **Style** : Épuré, business, traditionnel

**Basculer entre les thèmes** : Cliquer sur le bouton en haut à droite de l'interface.

## 🎯 Utilisation

### Interface POS
1. **Sélectionner des produits** : Cliquer sur les cartes de produits
2. **Utiliser le pavé numérique** : Saisir une quantité puis cliquer sur un produit
3. **Modifier les quantités** : Utiliser les boutons +/- dans le panier
4. **Traiter le paiement** : Cliquer sur "Payer" et confirmer

### Navigation
- **POS** : Interface principale de vente
- **Ventes** : Historique et détails des ventes
- **Rapports** : Graphiques et analytics

### Raccourcis Clavier
- `Ctrl + 1` : Aller à l'interface POS
- `Ctrl + 2` : Aller aux ventes
- `Ctrl + 3` : Aller aux rapports

## 🔧 Configuration

### Variables d'environnement
Créer un fichier `.env` (optionnel) :
```bash
SECRET_KEY=votre-clé-secrète
DATABASE_URL=sqlite:///pos_quantum.db
FLASK_ENV=development
```

### Personnalisation
- **Produits** : Modifier la fonction `init_products()` dans `app.py`
- **Taux de TVA** : Modifier `TAX_RATE` dans `static/js/api.js`
- **Thèmes** : Personnaliser les couleurs dans `static/css/themes.css`

## 🚀 Déploiement

### Production avec Gunicorn
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Docker (optionnel)
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🙏 Remerciements

- **Tailwind CSS** pour le framework CSS
- **Chart.js** pour les graphiques
- **Flask** pour le framework web Python
- **SQLAlchemy** pour l'ORM
