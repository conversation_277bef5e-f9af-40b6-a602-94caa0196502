# POS Futurist - Système de Caisse avec React et SQLite

Application de point de vente (POS) moderne développée avec React, TypeScript, Vite et SQLite.

## Fonctionnalités

- Gestion des produits (CRUD)
- Interface utilisateur réactive
- Base de données SQLite intégrée
- API RESTful
- Interface d'administration

## Prérequis

- Node.js >= 14.0.0
- npm ou yarn

## Installation

1. <PERSON><PERSON><PERSON> le dép<PERSON>t :

   ```bash
   git clone [URL_DU_DEPOT]
   cd pos_futurist_tsx
   ```

2. Installer les dépendances :

   ```bash
   npm install
   ```

## Démarrage

### Mode développement

Pour lancer à la fois le serveur de développement frontend et le serveur backend :

```bash
npm run dev:full
```

### Production

Pour construire et lancer en mode production :

```bash
npm run build
npm start
```

## Accès

- **Frontend** : `http://localhost:5173`
- **API** : `http://localhost:5000`

## Structure du projet

- `/src` - Code source du frontend React
- `/server` - Code source du backend
  - `/db` - Configuration et modèles de la base de données
  - `/models` - Modèles de données
  - `/routes` - Routes de l'API

## API Endpoints

- `GET /api/produits` - Récupérer tous les produits
- `POST /api/produits` - Créer un nouveau produit
- `GET /api/produits/:id` - Récupérer un produit par ID
- `PUT /api/produits/:id` - Mettre à jour un produit
- `DELETE /api/produits/:id` - Supprimer un produit
